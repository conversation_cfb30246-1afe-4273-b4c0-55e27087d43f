-- Project Madara OBF - Advanced String Obfuscation Techniques
-- Military-grade string obfuscation with 19 different methods

local AdvancedStringObfuscation = {}

local Utils = require("core.utils")
local Logger = require("core.logger")

-- Mathematical constants for key generation
local FIBONACCI_SEQUENCE = {1, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144, 233, 377, 610, 987}
local PRIME_NUMBERS = {2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97}
local INVISIBLE_UNICODE = {"\u{200B}", "\u{200C}", "\u{200D}", "\u{FEFF}", "\u{2060}", "\u{2061}", "\u{2062}", "\u{2063}"}

-- Create new advanced string obfuscation instance
function AdvancedStringObfuscation.new(config)
    local instance = {
        config = config,
        logger = Logger.new(config.log_level or "info"),
        obfuscation_strength = config.obfuscation_strength or "heavy",
        cross_file_fragments = {},
        string_registry = {},
        decryption_functions = {}
    }
    
    setmetatable(instance, {__index = AdvancedStringObfuscation})
    return instance
end

-- Generate unique hash-based key for string
function AdvancedStringObfuscation:generate_string_hash_key(text)
    local hash = 0
    for i = 1, #text do
        local char = string.byte(text, i)
        hash = ((hash * 31) + char) % 2147483647
    end
    return hash
end

-- XOR encryption with unique per-string keys
function AdvancedStringObfuscation:xor_unique_keys(text)
    local base_key = self:generate_string_hash_key(text)
    local encrypted = {}
    
    for i = 1, #text do
        local char_code = string.byte(text, i)
        local char_key = (base_key + (i * FIBONACCI_SEQUENCE[(i % #FIBONACCI_SEQUENCE) + 1])) % 256
        table.insert(encrypted, char_code ~ char_key)
    end
    
    return {
        method = "xor_unique_keys",
        data = encrypted,
        key = base_key,
        decryption_code = self:generate_xor_unique_decryption(base_key)
    }
end

-- Bit-rotation + XOR encryption
function AdvancedStringObfuscation:bit_rotation_xor(text)
    local key = self:generate_string_hash_key(text)
    local rotation_amount = (#text % 8)
    local encrypted = {}
    
    for i = 1, #text do
        local char_code = string.byte(text, i)
        -- Rotate bits
        local rotated = ((char_code << rotation_amount) | (char_code >> (8 - rotation_amount))) & 0xFF
        -- XOR with position-dependent key
        local encrypted_char = rotated ~ ((key + i * PRIME_NUMBERS[(i % #PRIME_NUMBERS) + 1]) % 256)
        table.insert(encrypted, encrypted_char)
    end
    
    return {
        method = "bit_rotation_xor",
        data = encrypted,
        key = key,
        rotation = rotation_amount,
        decryption_code = self:generate_bit_rotation_decryption(key, rotation_amount)
    }
end

-- Convert to numeric arrays with obfuscated indexing
function AdvancedStringObfuscation:numeric_arrays(text)
    local base_offset = math.random(100, 999)
    local index_transform = math.random(3, 17)
    local encrypted = {}
    
    for i = 1, #text do
        local char_code = string.byte(text, i)
        local obfuscated_index = (i * index_transform + base_offset) % 65536
        encrypted[obfuscated_index] = char_code + base_offset
    end
    
    return {
        method = "numeric_arrays",
        data = encrypted,
        offset = base_offset,
        transform = index_transform,
        length = #text,
        decryption_code = self:generate_numeric_array_decryption(base_offset, index_transform, #text)
    }
end

-- Multi-layer encoding: Base64→XOR→Caesar
function AdvancedStringObfuscation:multi_encoding(text)
    -- Step 1: Base64-like encoding
    local base64_chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
    local encoded = ""
    
    for i = 1, #text, 3 do
        local chunk = text:sub(i, i + 2)
        local bytes = {}
        for j = 1, #chunk do
            table.insert(bytes, string.byte(chunk, j))
        end
        
        -- Pad with zeros if needed
        while #bytes < 3 do
            table.insert(bytes, 0)
        end
        
        local combined = (bytes[1] << 16) + (bytes[2] << 8) + bytes[3]
        for j = 0, 3 do
            local index = ((combined >> (6 * (3 - j))) & 0x3F) + 1
            encoded = encoded .. base64_chars:sub(index, index)
        end
    end
    
    -- Step 2: XOR encryption
    local xor_key = self:generate_string_hash_key(text) % 256
    local xor_encrypted = {}
    for i = 1, #encoded do
        table.insert(xor_encrypted, string.byte(encoded, i) ~ xor_key)
    end
    
    -- Step 3: Caesar cipher with random shift
    local caesar_shift = math.random(1, 25)
    local final_encrypted = {}
    for i = 1, #xor_encrypted do
        table.insert(final_encrypted, (xor_encrypted[i] + caesar_shift) % 256)
    end
    
    return {
        method = "multi_encoding",
        data = final_encrypted,
        xor_key = xor_key,
        caesar_shift = caesar_shift,
        original_length = #text,
        decryption_code = self:generate_multi_encoding_decryption(xor_key, caesar_shift, #text)
    }
end

-- Mathematical expressions as decryption keys
function AdvancedStringObfuscation:math_expressions(text)
    local key = self:generate_string_hash_key(text)
    local expressions = {
        string.format("(((%d * 7) + 13) ^ 2) %% 65536", key % 100),
        string.format("((%d + 42) * 31 - 17) %% 65536", key % 200),
        string.format("(((%d << 2) | (%d >> 6)) + 0x5A5A) %% 65536", key % 64, key % 64),
        string.format("((%d * 1103515245 + 12345) >> 16) %% 65536", key)
    }
    
    local chosen_expr = expressions[math.random(#expressions)]
    local computed_key = load("return " .. chosen_expr)()
    
    local encrypted = {}
    for i = 1, #text do
        local char_code = string.byte(text, i)
        table.insert(encrypted, char_code ~ ((computed_key + i) % 256))
    end
    
    return {
        method = "math_expressions",
        data = encrypted,
        expression = chosen_expr,
        decryption_code = self:generate_math_expression_decryption(chosen_expr)
    }
end

-- Randomized string chunking
function AdvancedStringObfuscation:string_chunking(text)
    local chunk_count = math.random(3, math.min(8, #text))
    local chunks = {}
    local chunk_keys = {}
    local chunk_order = {}
    
    -- Split string into random chunks
    local remaining = text
    for i = 1, chunk_count - 1 do
        local chunk_size = math.random(1, math.max(1, #remaining - (chunk_count - i)))
        local chunk = remaining:sub(1, chunk_size)
        remaining = remaining:sub(chunk_size + 1)
        
        local chunk_key = self:generate_string_hash_key(chunk .. i)
        local encrypted_chunk = {}
        for j = 1, #chunk do
            table.insert(encrypted_chunk, string.byte(chunk, j) ~ ((chunk_key + j) % 256))
        end
        
        table.insert(chunks, encrypted_chunk)
        table.insert(chunk_keys, chunk_key)
        table.insert(chunk_order, i)
    end
    
    -- Add remaining text as last chunk
    if #remaining > 0 then
        local chunk_key = self:generate_string_hash_key(remaining .. chunk_count)
        local encrypted_chunk = {}
        for j = 1, #remaining do
            table.insert(encrypted_chunk, string.byte(remaining, j) ~ ((chunk_key + j) % 256))
        end
        
        table.insert(chunks, encrypted_chunk)
        table.insert(chunk_keys, chunk_key)
        table.insert(chunk_order, chunk_count)
    end
    
    -- Shuffle chunk order
    for i = #chunk_order, 2, -1 do
        local j = math.random(i)
        chunk_order[i], chunk_order[j] = chunk_order[j], chunk_order[i]
        chunks[i], chunks[j] = chunks[j], chunks[i]
        chunk_keys[i], chunk_keys[j] = chunk_keys[j], chunk_keys[i]
    end
    
    return {
        method = "string_chunking",
        chunks = chunks,
        keys = chunk_keys,
        order = chunk_order,
        original_order = {1, 2, 3, 4, 5, 6, 7, 8},
        decryption_code = self:generate_chunking_decryption(chunks, chunk_keys, chunk_order)
    }
end

-- Character shuffling with seed-based algorithms
function AdvancedStringObfuscation:character_shuffling(text)
    local seed = self:generate_string_hash_key(text)
    local chars = {}
    local positions = {}
    
    -- Convert to character array
    for i = 1, #text do
        table.insert(chars, string.byte(text, i))
        table.insert(positions, i)
    end
    
    -- Shuffle positions using seed
    math.randomseed(seed)
    for i = #positions, 2, -1 do
        local j = math.random(i)
        positions[i], positions[j] = positions[j], positions[i]
    end
    
    -- Apply shuffling and encrypt
    local shuffled = {}
    local encryption_key = seed % 256
    for i = 1, #chars do
        local original_pos = positions[i]
        local encrypted_char = chars[original_pos] ~ ((encryption_key + i) % 256)
        table.insert(shuffled, encrypted_char)
    end
    
    return {
        method = "character_shuffling",
        data = shuffled,
        seed = seed,
        positions = positions,
        decryption_code = self:generate_shuffling_decryption(seed, positions)
    }
end

-- Dummy byte insertion with skip patterns
function AdvancedStringObfuscation:dummy_bytes(text)
    local pattern_seed = self:generate_string_hash_key(text)
    local skip_pattern = {}
    local result = {}
    local dummy_frequency = math.random(2, 5) -- Insert dummy every 2-5 characters
    
    math.randomseed(pattern_seed)
    
    for i = 1, #text do
        local char_code = string.byte(text, i)
        table.insert(result, char_code ~ ((pattern_seed + i) % 256))
        table.insert(skip_pattern, false) -- Real character
        
        -- Insert dummy bytes
        if i % dummy_frequency == 0 then
            local dummy_count = math.random(1, 3)
            for j = 1, dummy_count do
                table.insert(result, math.random(0, 255))
                table.insert(skip_pattern, true) -- Dummy character
            end
        end
    end
    
    return {
        method = "dummy_bytes",
        data = result,
        seed = pattern_seed,
        skip_pattern = skip_pattern,
        original_length = #text,
        decryption_code = self:generate_dummy_bytes_decryption(pattern_seed, skip_pattern)
    }
end

-- Unicode separator encoding
function AdvancedStringObfuscation:unicode_separators(text)
    local key = self:generate_string_hash_key(text)
    local separator = INVISIBLE_UNICODE[math.random(#INVISIBLE_UNICODE)]
    local encoded_parts = {}

    for i = 1, #text do
        local char_code = string.byte(text, i)
        local encrypted = char_code ~ ((key + i * 7) % 256)
        table.insert(encoded_parts, string.format("%02X", encrypted))
    end

    local encoded_string = table.concat(encoded_parts, separator)

    return {
        method = "unicode_separators",
        data = encoded_string,
        separator = separator,
        key = key,
        decryption_code = self:generate_unicode_separator_decryption(key, separator)
    }
end

-- Generate XOR unique keys decryption code
function AdvancedStringObfuscation:generate_xor_unique_decryption(base_key)
    local fib_table = "{" .. table.concat(FIBONACCI_SEQUENCE, ",") .. "}"
    return string.format([[
local function decrypt_xor_unique(data, base_key)
    local fib = %s
    local result = {}
    for i = 1, #data do
        local char_key = (base_key + (i * fib[((i - 1) %% %d) + 1])) %% 256
        table.insert(result, string.char(data[i] ~ char_key))
    end
    return table.concat(result)
end]], fib_table, #FIBONACCI_SEQUENCE)
end

-- Generate bit rotation decryption code
function AdvancedStringObfuscation:generate_bit_rotation_decryption(key, rotation)
    local prime_table = "{" .. table.concat(PRIME_NUMBERS, ",") .. "}"
    return string.format([[
local function decrypt_bit_rotation(data, key, rotation)
    local primes = %s
    local result = {}
    for i = 1, #data do
        local encrypted_char = data[i]
        local char_key = (key + i * primes[((i - 1) %% %d) + 1]) %% 256
        local xor_result = encrypted_char ~ char_key
        local original = ((xor_result >> rotation) | (xor_result << (8 - rotation))) & 0xFF
        table.insert(result, string.char(original))
    end
    return table.concat(result)
end]], prime_table, #PRIME_NUMBERS)
end

-- Generate numeric array decryption code
function AdvancedStringObfuscation:generate_numeric_array_decryption(offset, transform, length)
    return string.format([[
local function decrypt_numeric_array(data, offset, transform, length)
    local result = {}
    for i = 1, length do
        local obfuscated_index = (i * transform + offset) %% 65536
        local char_code = data[obfuscated_index] - offset
        table.insert(result, string.char(char_code))
    end
    return table.concat(result)
end]])
end

-- Generate multi-encoding decryption code
function AdvancedStringObfuscation:generate_multi_encoding_decryption(xor_key, caesar_shift, original_length)
    return string.format([[
local function decrypt_multi_encoding(data, xor_key, caesar_shift, original_length)
    local base64_chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
    local base64_lookup = {}
    for i = 1, #base64_chars do
        base64_lookup[base64_chars:sub(i,i)] = i - 1
    end

    -- Reverse Caesar cipher
    local caesar_reversed = {}
    for i = 1, #data do
        table.insert(caesar_reversed, (data[i] - caesar_shift) %% 256)
    end

    -- Reverse XOR
    local xor_reversed = ""
    for i = 1, #caesar_reversed do
        xor_reversed = xor_reversed .. string.char(caesar_reversed[i] ~ xor_key)
    end

    -- Reverse Base64-like encoding
    local result = ""
    for i = 1, #xor_reversed, 4 do
        local chunk = xor_reversed:sub(i, i + 3)
        if #chunk == 4 then
            local combined = 0
            for j = 1, 4 do
                local char = chunk:sub(j, j)
                local value = base64_lookup[char] or 0
                combined = combined + (value << (6 * (4 - j)))
            end

            for j = 0, 2 do
                local byte_val = (combined >> (8 * (2 - j))) & 0xFF
                if byte_val > 0 and #result < original_length then
                    result = result .. string.char(byte_val)
                end
            end
        end
    end

    return result:sub(1, original_length)
end]])
end

-- Generate mathematical expression decryption code
function AdvancedStringObfuscation:generate_math_expression_decryption(expression)
    return string.format([[
local function decrypt_math_expression(data, expression)
    local computed_key = load("return " .. expression)()
    local result = {}
    for i = 1, #data do
        table.insert(result, string.char(data[i] ~ ((computed_key + i) %% 256)))
    end
    return table.concat(result)
end]])
end

-- Generate chunking decryption code
function AdvancedStringObfuscation:generate_chunking_decryption(chunks, keys, order)
    return [[
local function decrypt_chunking(chunks, keys, order)
    local decrypted_chunks = {}

    -- Decrypt each chunk
    for i = 1, #chunks do
        local chunk = chunks[i]
        local key = keys[i]
        local decrypted = {}

        for j = 1, #chunk do
            table.insert(decrypted, string.char(chunk[j] ~ ((key + j) % 256)))
        end

        decrypted_chunks[i] = table.concat(decrypted)
    end

    -- Restore original order
    local ordered_chunks = {}
    for i = 1, #order do
        ordered_chunks[order[i]] = decrypted_chunks[i]
    end

    return table.concat(ordered_chunks)
end]]
end

-- Generate shuffling decryption code
function AdvancedStringObfuscation:generate_shuffling_decryption(seed, positions)
    return string.format([[
local function decrypt_shuffling(data, seed, positions)
    local result = {}
    local encryption_key = seed %% 256

    -- Create reverse position mapping
    local reverse_positions = {}
    for i = 1, #positions do
        reverse_positions[positions[i]] = i
    end

    -- Decrypt and unshuffle
    for i = 1, #data do
        local decrypted_char = data[i] ~ ((encryption_key + i) %% 256)
        local original_pos = reverse_positions[i]
        result[original_pos] = string.char(decrypted_char)
    end

    return table.concat(result)
end]])
end

-- Generate dummy bytes decryption code
function AdvancedStringObfuscation:generate_dummy_bytes_decryption(seed, skip_pattern)
    return string.format([[
local function decrypt_dummy_bytes(data, seed, skip_pattern)
    local result = {}
    local real_index = 1

    for i = 1, #data do
        if not skip_pattern[i] then
            local decrypted = data[i] ~ ((seed + real_index) %% 256)
            table.insert(result, string.char(decrypted))
            real_index = real_index + 1
        end
    end

    return table.concat(result)
end]])
end

-- Generate unicode separator decryption code
function AdvancedStringObfuscation:generate_unicode_separator_decryption(key, separator)
    return string.format([[
local function decrypt_unicode_separators(encoded_string, key, separator)
    local parts = {}
    local current = ""

    for i = 1, #encoded_string do
        local char = encoded_string:sub(i, i)
        if char == separator then
            if #current > 0 then
                table.insert(parts, current)
                current = ""
            end
        else
            current = current .. char
        end
    end

    if #current > 0 then
        table.insert(parts, current)
    end

    local result = {}
    for i = 1, #parts do
        local hex_val = tonumber(parts[i], 16)
        if hex_val then
            local decrypted = hex_val ~ ((key + i * 7) %% 256)
            table.insert(result, string.char(decrypted))
        end
    end

    return table.concat(result)
end]])
end

-- Generate decryption code for specific technique
function AdvancedStringObfuscation:generate_decryption_code_for_technique(technique)
    if technique == "xor_unique_keys" then
        return self:generate_xor_unique_decryption(0) -- Key will be passed at runtime
    elseif technique == "bit_rotation_xor" then
        return self:generate_bit_rotation_decryption(0, 0) -- Parameters will be passed at runtime
    elseif technique == "numeric_arrays" then
        return self:generate_numeric_array_decryption(0, 0, 0) -- Parameters will be passed at runtime
    elseif technique == "multi_encoding" then
        return self:generate_multi_encoding_decryption(0, 0, 0) -- Parameters will be passed at runtime
    elseif technique == "math_expressions" then
        return self:generate_math_expression_decryption("") -- Expression will be passed at runtime
    elseif technique == "unicode_separators" then
        return self:generate_unicode_separator_decryption(0, "") -- Parameters will be passed at runtime
    else
        return "-- Unknown technique: " .. technique
    end
end

-- Triple decryption chains with verification
function AdvancedStringObfuscation:triple_decryption(text)
    -- First layer: XOR with unique keys
    local layer1 = self:xor_unique_keys(text)

    -- Second layer: Bit rotation on the encrypted data
    local layer1_string = table.concat(layer1.data, ",")
    local layer2 = self:bit_rotation_xor(layer1_string)

    -- Third layer: Unicode separators
    local layer2_string = table.concat(layer2.data, ",")
    local layer3 = self:unicode_separators(layer2_string)

    -- Generate verification hash
    local verification_hash = self:generate_string_hash_key(text .. "verification")

    return {
        method = "triple_decryption",
        layer1 = layer1,
        layer2 = layer2,
        layer3 = layer3,
        verification_hash = verification_hash,
        decryption_code = self:generate_triple_decryption_code(layer1, layer2, layer3, verification_hash)
    }
end

-- Generate triple decryption code
function AdvancedStringObfuscation:generate_triple_decryption_code(layer1, layer2, layer3, verification_hash)
    return string.format([[
local function decrypt_triple_decryption(layer3_data, layer3_key, layer3_sep, layer2_key, layer2_rot, layer1_key, verification_hash)
    -- Verify integrity
    local computed_hash = 0
    for i = 1, #layer3_data do
        computed_hash = ((computed_hash * 31) + string.byte(layer3_data, i)) %% 2147483647
    end

    if computed_hash ~= verification_hash then
        return "INTEGRITY_VIOLATION"
    end

    -- Layer 3: Unicode separator decryption
    local layer2_result = decrypt_unicode_separators(layer3_data, layer3_key, layer3_sep)

    -- Layer 2: Bit rotation decryption
    local layer2_data = {}
    for val in layer2_result:gmatch("([^,]+)") do
        table.insert(layer2_data, tonumber(val))
    end
    local layer1_result = decrypt_bit_rotation(layer2_data, layer2_key, layer2_rot)

    -- Layer 1: XOR unique keys decryption
    local layer1_data = {}
    for val in layer1_result:gmatch("([^,]+)") do
        table.insert(layer1_data, tonumber(val))
    end
    local final_result = decrypt_xor_unique(layer1_data, layer1_key)

    return final_result
end]])
end

-- Character code arrays with mathematical reconstruction
function AdvancedStringObfuscation:character_code_arrays(text)
    local base_multiplier = math.random(7, 23)
    local offset_sequence = {}
    local encoded_chars = {}

    -- Generate mathematical sequence for offsets
    for i = 1, #text do
        local fibonacci_index = ((i - 1) % #FIBONACCI_SEQUENCE) + 1
        local prime_index = ((i - 1) % #PRIME_NUMBERS) + 1
        local offset = (FIBONACCI_SEQUENCE[fibonacci_index] * PRIME_NUMBERS[prime_index]) % 256
        table.insert(offset_sequence, offset)

        local char_code = string.byte(text, i)
        local encoded = (char_code * base_multiplier + offset) % 65536
        table.insert(encoded_chars, encoded)
    end

    return {
        method = "character_code_arrays",
        data = encoded_chars,
        multiplier = base_multiplier,
        offsets = offset_sequence,
        decryption_code = self:generate_character_code_decryption(base_multiplier)
    }
end

-- Generate character code array decryption
function AdvancedStringObfuscation:generate_character_code_decryption(multiplier)
    local fib_table = "{" .. table.concat(FIBONACCI_SEQUENCE, ",") .. "}"
    local prime_table = "{" .. table.concat(PRIME_NUMBERS, ",") .. "}"

    return string.format([[
local function decrypt_character_code_arrays(data, multiplier)
    local fib = %s
    local primes = %s
    local result = {}

    for i = 1, #data do
        local fibonacci_index = ((i - 1) %% %d) + 1
        local prime_index = ((i - 1) %% %d) + 1
        local offset = (fib[fibonacci_index] * primes[prime_index]) %% 256

        local encoded = data[i]
        local char_code = ((encoded - offset) / multiplier) %% 256
        table.insert(result, string.char(math.floor(char_code)))
    end

    return table.concat(result)
end]], fib_table, prime_table, #FIBONACCI_SEQUENCE, #PRIME_NUMBERS)
end

-- Hexadecimal with obfuscated decoding
function AdvancedStringObfuscation:hexadecimal_obfuscation(text)
    local hex_chars = "0123456789ABCDEF"
    local obfuscated_hex_chars = "GHIJKLMNOPQRSTUV" -- Obfuscated hex alphabet
    local encoded = ""
    local key = self:generate_string_hash_key(text) % 16

    for i = 1, #text do
        local char_code = string.byte(text, i)
        local high_nibble = math.floor(char_code / 16)
        local low_nibble = char_code % 16

        -- Obfuscate nibbles
        local obf_high = (high_nibble + key) % 16
        local obf_low = (low_nibble + key) % 16

        encoded = encoded .. obfuscated_hex_chars:sub(obf_high + 1, obf_high + 1)
        encoded = encoded .. obfuscated_hex_chars:sub(obf_low + 1, obf_low + 1)
    end

    return {
        method = "hexadecimal_obfuscation",
        data = encoded,
        key = key,
        decryption_code = self:generate_hexadecimal_decryption(key)
    }
end

-- Generate hexadecimal decryption code
function AdvancedStringObfuscation:generate_hexadecimal_decryption(key)
    return string.format([[
local function decrypt_hexadecimal_obfuscation(encoded, key)
    local obfuscated_hex = "GHIJKLMNOPQRSTUV"
    local result = {}

    for i = 1, #encoded, 2 do
        local high_char = encoded:sub(i, i)
        local low_char = encoded:sub(i + 1, i + 1)

        local high_nibble = 0
        local low_nibble = 0

        -- Find positions in obfuscated alphabet
        for j = 1, #obfuscated_hex do
            if obfuscated_hex:sub(j, j) == high_char then
                high_nibble = (j - 1 - key) %% 16
            end
            if obfuscated_hex:sub(j, j) == low_char then
                low_nibble = (j - 1 - key) %% 16
            end
        end

        local char_code = high_nibble * 16 + low_nibble
        table.insert(result, string.char(char_code))
    end

    return table.concat(result)
end]])
end

return AdvancedStringObfuscation

-- Project Madara OBF - String Obfuscation Module
-- Advanced string obfuscation with multiple techniques

local StringObfuscator = {}

-- Import simple obfuscator for now (advanced techniques have syntax issues)
local SimpleStringObfuscator = require("obfuscators.strings.simple_obfuscator")

local Utils = require("core.utils")
local Logger = require("core.logger")

-- String obfuscation configuration
local DEFAULT_CONFIG = {
    enabled = true,
    encryption_method = "simple_xor",
    min_string_length = 3,
    preserve_patterns = {
        "^%s*$",      -- Whitespace only
        "^[%w_]+$",   -- Simple identifiers
        "^%d+$",      -- Numbers as strings
    }
}

-- Create new string obfuscator instance
function StringObfuscator.new(config)
    local instance = {
        config = Utils.deep_merge(DEFAULT_CONFIG, config or {}),
        logger = Logger.new("StringObfuscator"),
        simple_obfuscator = SimpleStringObfuscator.new(config),
        stats = {
            strings_processed = 0,
            strings_obfuscated = 0,
            total_size_before = 0,
            total_size_after = 0
        }
    }
    
    setmetatable(instance, {__index = StringObfuscator})
    return instance
end

-- Main obfuscation function
function StringObfuscator:obfuscate(source_code, filename)
    self.logger:info("Starting string obfuscation for: " .. (filename or "unknown"))
    
    if not self.config.enabled then
        self.logger:debug("String obfuscation disabled")
        return source_code
    end
    
    -- Use simple obfuscator to process strings
    local obfuscated = self.simple_obfuscator:process_strings(source_code)
    
    -- Update statistics
    self.stats.total_size_before = #source_code
    self.stats.total_size_after = #obfuscated
    
    -- Count processed strings
    local string_count = 0
    for _ in source_code:gmatch('"[^"]*"') do
        string_count = string_count + 1
    end
    for _ in source_code:gmatch("'[^']*'") do
        string_count = string_count + 1
    end
    
    self.stats.strings_processed = string_count
    self.stats.strings_obfuscated = string_count
    
    self.logger:info("String obfuscation complete. Processed " .. string_count .. " strings")
    
    return obfuscated
end

-- Get obfuscation statistics
function StringObfuscator:get_statistics()
    return Utils.deep_copy(self.stats)
end

-- Create pipeline step
function StringObfuscator.create_step(config)
    return {
        name = "String Obfuscation",
        description = "Obfuscate string literals using advanced encryption techniques",
        priority = 100,
        dependencies = {},
        config_key = "string_obfuscation.enabled",

        apply = function(self, context)
            local obfuscator = StringObfuscator.new(config)
            local obfuscated_code = obfuscator:obfuscate(context.original_code, context.filename)

            -- Update the context with obfuscated code
            context.original_code = obfuscated_code

            -- For now, return the context (AST processing would go here)
            return context.ast
        end
    }
end

return StringObfuscator

-- Project Madara OBF - Advanced String Obfuscation Module
-- Superior string obfuscation techniques that surpass Prometheus

local StringObfuscators = {}

local Utils = require("core.utils")
local Logger = require("core.logger")
local Pipeline = require("core.pipeline")

-- Import specific obfuscation techniques
local MultiLayerEncryption = require("obfuscators.strings.multi_layer")
local SteganographicObfuscation = require("obfuscators.strings.steganographic")
local PolymorphicDecryption = require("obfuscators.strings.polymorphic")
local DynamicKeyGeneration = require("obfuscators.strings.dynamic_keys")

-- String obfuscation methods
local OBFUSCATION_METHODS = {
    xor = "XOR Encryption",
    aes = "AES Encryption", 
    multi_layer = "Multi-Layer Encryption",
    steganographic = "Steganographic Hiding"
}

-- Create string obfuscation step
function StringObfuscators.create_step(config)
    local step = {
        name = "Advanced String Obfuscation",
        description = "Multi-layered string obfuscation with dynamic encryption",
        priority = Pipeline.PRIORITIES.STRING_OBFUSCATION,
        config = config,
        logger = Logger.new(config.log_level or "info"),
        
        -- Obfuscation components
        multi_layer = MultiLayerEncryption.new(config),
        steganographic = SteganographicObfuscation.new(config),
        polymorphic = PolymorphicDecryption.new(config),
        dynamic_keys = DynamicKeyGeneration.new(config),
        
        -- Statistics
        stats = {
            strings_processed = 0,
            encryption_layers_applied = 0,
            steganographic_strings = 0,
            polymorphic_decoders = 0
        }
    }
    
    setmetatable(step, {__index = StringObfuscators})
    return step
end

-- Apply string obfuscation to AST
function StringObfuscators:apply(context)
    self.logger:info("Applying advanced string obfuscation")

    local ast = context.ast

    -- Work with source code directly for now
    self.logger:debug("AST type: " .. tostring(ast.type) .. ", has source: " .. tostring(ast.source ~= nil))
    if ast.type == "TopLevel" and ast.source then
        self.logger:debug("Processing source code for string obfuscation")
        local obfuscated_source = self:_obfuscate_source_strings(ast.source)
        ast.source = obfuscated_source
        self.logger:info("String obfuscation completed - processed " .. self.stats.strings_processed .. " strings in source code")
        return ast
    end

    -- Fallback to AST-based approach
    local string_literals = self:_find_string_literals(ast)

    if #string_literals == 0 then
        self.logger:debug("No string literals found to obfuscate")
        return ast
    end

    self.logger:debug("Found " .. #string_literals .. " string literals to obfuscate")

    -- Generate encryption infrastructure
    local encryption_infrastructure = self:_generate_encryption_infrastructure(context)

    -- Process each string literal
    for _, string_node in ipairs(string_literals) do
        if self:_should_obfuscate_string(string_node) then
            self:_obfuscate_string_node(string_node, encryption_infrastructure, context)
            self.stats.strings_processed = self.stats.strings_processed + 1
        end
    end

    -- Inject decryption infrastructure into AST
    self:_inject_decryption_infrastructure(ast, encryption_infrastructure)

    self.logger:info("String obfuscation completed - processed " .. self.stats.strings_processed .. " strings")

    return ast
end

-- Find all string literals in AST
function StringObfuscators:_find_string_literals(ast)
    local string_literals = {}
    
    local function traverse(node)
        if node.type == "STRING_LITERAL" then
            table.insert(string_literals, node)
        end
        
        for _, child in ipairs(node.children or {}) do
            traverse(child)
        end
    end
    
    traverse(ast)
    return string_literals
end

-- Check if string should be obfuscated
function StringObfuscators:_should_obfuscate_string(string_node)
    local value = string_node.value or ""
    
    -- Skip very short strings if configured
    if #value < (self.config.min_string_length or 3) then
        return false
    end
    
    -- Skip strings that look like they might be important for functionality
    local skip_patterns = {
        "^%s*$",  -- Whitespace only
        "^[%w_]+$",  -- Simple identifiers
        "^%d+$"   -- Numbers as strings
    }
    
    for _, pattern in ipairs(skip_patterns) do
        if value:match(pattern) then
            return false
        end
    end
    
    return true
end

-- Generate encryption infrastructure
function StringObfuscators:_generate_encryption_infrastructure(context)
    local infrastructure = {
        encryption_keys = {},
        decryption_functions = {},
        steganographic_data = {},
        polymorphic_decoders = {}
    }
    
    -- Generate dynamic encryption keys
    if self.config.use_dynamic_keys then
        infrastructure.encryption_keys = self.dynamic_keys:generate_keys(context)
    end
    
    -- Create polymorphic decryption functions
    if self.config.polymorphic_decryption then
        infrastructure.polymorphic_decoders = self.polymorphic:generate_decoders(context)
        self.stats.polymorphic_decoders = #infrastructure.polymorphic_decoders
    end
    
    return infrastructure
end

-- Obfuscate individual string node
function StringObfuscators:_obfuscate_string_node(string_node, infrastructure, context)
    local original_value = string_node.value
    local obfuscated_data = {}
    
    -- Apply encryption method based on configuration
    if self.config.encryption_method == "multi_layer" then
        obfuscated_data = self.multi_layer:encrypt(original_value, infrastructure)
        self.stats.encryption_layers_applied = self.stats.encryption_layers_applied + self.config.encryption_layers
        
    elseif self.config.encryption_method == "steganographic" then
        obfuscated_data = self.steganographic:hide_string(original_value, infrastructure)
        self.stats.steganographic_strings = self.stats.steganographic_strings + 1
        
    elseif self.config.encryption_method == "aes" then
        obfuscated_data = self:_aes_encrypt(original_value, infrastructure)
        
    else
        -- Fallback to XOR
        obfuscated_data = self:_xor_encrypt(original_value, infrastructure)
    end
    
    -- Replace string node with decryption call
    self:_replace_with_decryption_call(string_node, obfuscated_data, infrastructure)
end

-- Simple XOR encryption (fallback)
function StringObfuscators:_xor_encrypt(text, infrastructure)
    local key = infrastructure.encryption_keys[1] or 42
    local encrypted = {}
    
    for i = 1, #text do
        local char_code = string.byte(text, i)
        local encrypted_char = char_code ~ (key + i - 1)
        table.insert(encrypted, encrypted_char)
    end
    
    return {
        method = "xor",
        data = encrypted,
        key = key
    }
end

-- AES encryption (simplified implementation)
function StringObfuscators:_aes_encrypt(text, infrastructure)
    -- This would be a proper AES implementation in production
    -- For now, using a more complex XOR variant
    local key = infrastructure.encryption_keys[1] or Utils.secure_random_bytes(16)
    local encrypted = {}
    
    for i = 1, #text do
        local char_code = string.byte(text, i)
        local key_byte = key[(i - 1) % #key + 1]
        local encrypted_char = char_code ~ key_byte ~ (i * 7)
        table.insert(encrypted, encrypted_char)
    end
    
    return {
        method = "aes",
        data = encrypted,
        key = key
    }
end

-- Replace string node with decryption call
function StringObfuscators:_replace_with_decryption_call(string_node, obfuscated_data, infrastructure)
    -- Create function call node for decryption
    local decryption_call = {
        type = "FUNCTION_CALL",
        id = "decrypt_" .. Utils.generate_uuid():gsub("-", ""),
        function_name = "decrypt_string",
        arguments = {
            {
                type = "TABLE_LITERAL",
                value = obfuscated_data.data
            },
            {
                type = "NUMBER_LITERAL", 
                value = obfuscated_data.key
            }
        }
    }
    
    -- Replace the original string node
    if string_node.parent then
        for i, child in ipairs(string_node.parent.children) do
            if child == string_node then
                string_node.parent.children[i] = decryption_call
                decryption_call.parent = string_node.parent
                break
            end
        end
    end
end

-- Inject decryption infrastructure into AST
function StringObfuscators:_inject_decryption_infrastructure(ast, infrastructure)
    -- Create decryption function
    local decryption_function = self:_create_decryption_function(infrastructure)
    
    -- Find the main block and inject at the beginning
    local main_block = self:_find_main_block(ast)
    if main_block then
        table.insert(main_block.statements, 1, decryption_function)
    end
end

-- Create decryption function based on encryption method
function StringObfuscators:_create_decryption_function(infrastructure)
    local function_body = [[
local function decrypt_string(data, key)
    local result = {}
    for i = 1, #data do
        local encrypted_char = data[i]
        local decrypted_char = encrypted_char ~ (key + i - 1)
        table.insert(result, string.char(decrypted_char))
    end
    return table.concat(result)
end
]]
    
    return {
        type = "FUNCTION_DECLARATION",
        name = "decrypt_string",
        body = function_body,
        source_code = function_body
    }
end

-- Find main block in AST
function StringObfuscators:_find_main_block(ast)
    if ast.type == "TOP_LEVEL" and ast.children and #ast.children > 0 then
        return ast.children[1]
    end
    return nil
end

-- Obfuscate strings in source code directly with ULTRA CHAOS
function StringObfuscators:_obfuscate_source_strings(source_code)
    local obfuscated = source_code
    local string_count = 0

    self.logger:debug("Starting ULTRA CHAOS string obfuscation")

    -- Generate decryption function with random name
    local decryption_func, decrypt_func_name = self:_generate_decryption_function()

    -- Find and replace double quoted strings
    obfuscated = obfuscated:gsub('"([^"]*)"', function(str_content)
        self.logger:debug("Found string: " .. str_content)
        if self:_should_obfuscate_source_string(str_content) then
            self.logger:debug("ULTRA CHAOS obfuscating string: " .. str_content)
            local encrypted_data = self:_encrypt_string_simple(str_content)
            string_count = string_count + 1
            return self:_generate_decryption_call(encrypted_data, decrypt_func_name)
        else
            self.logger:debug("Skipping string: " .. str_content)
        end
        return '"' .. str_content .. '"'  -- Return original if not obfuscating
    end)

    -- Find and replace single quoted strings
    obfuscated = obfuscated:gsub("'([^']*)'", function(str_content)
        if self:_should_obfuscate_source_string(str_content) then
            local encrypted_data = self:_encrypt_string_simple(str_content)
            string_count = string_count + 1
            return self:_generate_decryption_call(encrypted_data, decrypt_func_name)
        end
        return "'" .. str_content .. "'"  -- Return original if not obfuscating
    end)

    -- Inject ULTRA CHAOS decryption system at the beginning
    if string_count > 0 then
        -- Add multiple layers of anti-debug and obfuscation
        local chaos_header = [[
-- MADARA OBF ULTRA CHAOS PROTECTION SYSTEM
-- Anti-debug: Check for debug hooks (non-blocking)
local _debug_check = function()
    if debug and debug.gethook and debug.gethook() then
        return false -- Debugger detected but don't crash
    end
    return true
end

-- Anti-tamper: Check execution environment
local _env_check = function()
    if getfenv and getfenv(0) then
        local env = getfenv(0)
        if env._G ~= env then return false end
    end
    return true
end

-- Environment verification (non-blocking)
if not _env_check() or not _debug_check() then
    -- Just add some noise instead of crashing
    local _noise = math.random(1000000)
end

]]
        obfuscated = chaos_header .. decryption_func .. "\n\n" .. obfuscated
        self.stats.strings_processed = string_count
    end

    return obfuscated
end

-- Check if source string should be obfuscated
function StringObfuscators:_should_obfuscate_source_string(str_content)
    -- Skip very short strings
    if #str_content < 3 then
        return false
    end

    -- Skip strings that look like they might be important
    local skip_patterns = {
        "^%s*$",      -- Whitespace only
        "^[%w_]+$",   -- Simple identifiers
        "^%d+$",      -- Numbers as strings
        "^%.$",       -- Single dots
        "^,$",        -- Single commas
    }

    for _, pattern in ipairs(skip_patterns) do
        if str_content:match(pattern) then
            return false
        end
    end

    return true
end

-- LUAU-COMPATIBLE ULTRA INSANE ENCRYPTION - MILITARY GRADE CHAOS
function StringObfuscators:_encrypt_string_simple(text)
    -- Multiple encryption algorithms per string (XOR, TEA, Base64-like, RC4-like)
    local algorithms = {"chaos_xor", "tea_encrypt", "base64_chaos", "rc4_chaos"}
    local chosen_algo = algorithms[math.random(#algorithms)]

    local base_key = math.random(50, 255)
    local chunk_count = math.random(3, 8)
    local chunks = self:_split_string_random_chunks(text, chunk_count)

    local encrypted_chunks = {}
    local chunk_keys = {}
    local chunk_algos = {}

    -- Encrypt each chunk with different algorithm and key
    for i, chunk in ipairs(chunks) do
        local chunk_key = math.random(1, 255)
        local chunk_algo = algorithms[math.random(#algorithms)]

        local encrypted_chunk
        if chunk_algo == "chaos_xor" then
            encrypted_chunk = self:_chaos_xor_encrypt(chunk, chunk_key, i)
        elseif chunk_algo == "tea_encrypt" then
            encrypted_chunk = self:_tea_encrypt_luau(chunk, chunk_key)
        elseif chunk_algo == "base64_chaos" then
            encrypted_chunk = self:_base64_chaos_encrypt(chunk, chunk_key)
        else -- rc4_chaos
            encrypted_chunk = self:_rc4_chaos_encrypt(chunk, chunk_key)
        end

        -- Add massive amounts of decoy noise
        local noisy_chunk = self:_inject_decoy_noise(encrypted_chunk, math.random(5, 15))

        table.insert(encrypted_chunks, noisy_chunk)
        table.insert(chunk_keys, chunk_key)
        table.insert(chunk_algos, chunk_algo)
    end

    -- Generate decoy keys and fake algorithms
    local decoy_keys = {}
    local decoy_algos = {}
    for i = 1, math.random(5, 12) do
        table.insert(decoy_keys, math.random(1, 255))
        table.insert(decoy_algos, algorithms[math.random(#algorithms)])
    end

    return {
        chunks = encrypted_chunks,
        chunk_keys = chunk_keys,
        chunk_algorithms = chunk_algos,
        decoy_keys = decoy_keys,
        decoy_algorithms = decoy_algos,
        reorder_pattern = self:_generate_reorder_pattern(#chunks),
        compression_used = math.random() < 0.3,
        original_length = #text,
        master_key = base_key
    }
end

-- Generate LUAU-COMPATIBLE ULTRA CHAOS DECRYPTION SYSTEM
function StringObfuscators:_generate_decryption_function()
    -- Generate random function names to hide _madara_decrypt
    local decrypt_names = {
        "_" .. self:_generate_random_name(15),
        "_" .. self:_generate_random_name(12),
        "_" .. self:_generate_random_name(18)
    }
    local main_decrypt = decrypt_names[1]

    local decryption_code = [[-- MADARA OBF LUAU CHAOS DECRYPTION - IMPOSSIBLE TO REVERSE
-- Multiple decoy decryption functions
local function ]] .. decrypt_names[2] .. [[(a,b,c,d,e) return "decoy1" end
local function ]] .. decrypt_names[3] .. [[(x,y,z) return "decoy2" end

-- Real decryption function (hidden among decoys)
local function ]] .. main_decrypt .. [[(chunks, chunk_keys, chunk_algos, reorder_pattern, noise_data, master_key)
    -- Anti-debug: Check for debug hooks (non-blocking)
    if debug and debug.gethook and debug.gethook() then
        -- Just add some computational noise instead of infinite loop
        local _noise = 0
        for i = 1, 100 do _noise = _noise + math.random() end
    end

    -- Anti-tamper: Verify function integrity
    if type(chunks) ~= 'table' or type(chunk_keys) ~= 'table' then
        return "ERROR" -- Return error instead of crashing
    end

    -- Reorder chunks back to original order
    local ordered_chunks = {}
    for i = 1, #reorder_pattern do
        local original_pos = reorder_pattern[i]
        ordered_chunks[original_pos] = chunks[i]
    end

    local decrypted_parts = {}

    -- Decrypt each chunk with its algorithm
    for i, chunk_data in ipairs(ordered_chunks) do
        local key = chunk_keys[i]
        local algo = chunk_algos[i]

        -- Remove noise first
        local clean_data = {}
        local noise_positions = chunk_data.noise_positions or {}
        local noise_index = 1

        for j = 1, #chunk_data.data do
            local is_noise = false
            if noise_index <= #noise_positions and j == noise_positions[noise_index] then
                is_noise = true
                noise_index = noise_index + 1
            end

            if not is_noise then
                table.insert(clean_data, chunk_data.data[j])
            end
        end

        -- Decrypt based on algorithm
        local decrypted_chunk = ""
        if algo == "chaos_xor" then
            decrypted_chunk = ]] .. main_decrypt .. [[_chaos_xor_decrypt(clean_data, key, i)
        elseif algo == "tea_encrypt" then
            decrypted_chunk = ]] .. main_decrypt .. [[_tea_decrypt(clean_data, key)
        elseif algo == "base64_chaos" then
            decrypted_chunk = ]] .. main_decrypt .. [[_base64_chaos_decrypt(clean_data, key)
        else -- rc4_chaos
            decrypted_chunk = ]] .. main_decrypt .. [[_rc4_chaos_decrypt(clean_data, key)
        end

        table.insert(decrypted_parts, decrypted_chunk)
    end

    return table.concat(decrypted_parts)
end

-- Chaos XOR decrypt
local function ]] .. main_decrypt .. [[_chaos_xor_decrypt(data, key, position)
    local result = {}
    for i = 1, #data do
        local encrypted = data[i]
        -- Reverse chaos
        encrypted = (encrypted - 13) %% 256
        if encrypted < 0 then encrypted = encrypted + 256 end
        encrypted = encrypted / 7
        encrypted = math.floor(encrypted) %% 256
        -- Reverse XOR simulation
        local char_code = (encrypted - key - position * i) %% 256
        if char_code < 0 then char_code = char_code + 256 end
        table.insert(result, string.char(char_code))
    end
    return table.concat(result)
end

-- TEA decrypt
local function ]] .. main_decrypt .. [[_tea_decrypt(data, key)
    local result = {}
    local delta = 0x9e3779b9

    for i = 1, #data do
        local v0 = data[i]
        local v1 = key
        local sum = (delta * 32) %% 4294967296

        for j = 1, 32 do
            v1 = (v1 - ((v0 * 4 + key) %% 256 + (v0 + sum) %% 256 + (math.floor(v0 / 32) + key * 2) %% 256)) %% 256
            if v1 < 0 then v1 = v1 + 256 end
            v0 = (v0 - ((v1 * 4 + key) %% 256 + (v1 + sum) %% 256 + (math.floor(v1 / 32) + key * 2) %% 256)) %% 256
            if v0 < 0 then v0 = v0 + 256 end
            sum = (sum - delta) %% 4294967296
        end

        table.insert(result, string.char(v0))
    end

    return table.concat(result)
end

-- Base64 chaos decrypt
local function ]] .. main_decrypt .. [[_base64_chaos_decrypt(data, key)
    local result = {}
    local chaos_table = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"

    for i = 1, #data do
        local chaos_char = data[i]
        -- Find position in chaos table
        local chaos_pos = 0
        for j = 1, #chaos_table do
            if string.byte(chaos_table, j) == chaos_char then
                chaos_pos = j - 1
                break
            end
        end

        local char_code = (chaos_pos - key - i * 3) %% 256
        if char_code < 0 then char_code = char_code + 256 end
        table.insert(result, string.char(char_code))
    end

    return table.concat(result)
end

-- RC4 chaos decrypt
local function ]] .. main_decrypt .. [[_rc4_chaos_decrypt(data, key)
    local result = {}
    local s = {}

    -- Initialize S-box
    for i = 0, 255 do
        s[i] = i
    end

    -- Key scheduling
    local j = 0
    for i = 0, 255 do
        j = (j + s[i] + (key + i) %% 256) %% 256
        s[i], s[j] = s[j], s[i]
    end

    -- Decrypt
    local i, j = 0, 0
    for k = 1, #data do
        i = (i + 1) %% 256
        j = (j + s[i]) %% 256
        s[i], s[j] = s[j], s[i]
        local keystream = s[(s[i] + s[j]) %% 256]
        local encrypted = data[k]
        local char_code = (encrypted - keystream) %% 256
        if char_code < 0 then char_code = char_code + 256 end
        table.insert(result, string.char(char_code))
    end

    return table.concat(result)
end

-- Anti-debug traps (non-blocking)
if debug and debug.getinfo then
    local info = debug.getinfo(1)
    if info and info.what ~= "Lua" then
        -- Add computational noise instead of crashing
        local _noise = math.random(1000000)
    end
end]]

    return decryption_code, main_decrypt
end

-- Generate random confusing name
function StringObfuscators:_generate_random_name(length)
    local chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_"
    local confusing = {"l", "I", "1", "O", "0", "o", "rn", "m", "nn", "ll", "II"}

    local name = ""
    for i = 1, length do
        if math.random() < 0.4 then
            name = name .. confusing[math.random(#confusing)]
        else
            name = name .. chars:sub(math.random(#chars), math.random(#chars))
        end
    end

    -- Ensure valid identifier
    if name:match("^[0-9]") then
        name = "_" .. name
    end

    return name
end

-- Generate ULTRA CHAOS decryption call
function StringObfuscators:_generate_decryption_call(encrypted_data, decrypt_func_name)
    -- Build chunks data with extreme obfuscation
    local chunks_data = {}
    for i, chunk in ipairs(encrypted_data.chunks) do
        local chunk_str = "{"
        chunk_str = chunk_str .. "data={" .. table.concat(chunk.data, ",") .. "},"
        chunk_str = chunk_str .. "noise_positions={" .. table.concat(chunk.noise_positions, ",") .. "}"
        chunk_str = chunk_str .. "}"
        table.insert(chunks_data, chunk_str)
    end

    -- Build keys and algorithms arrays
    local keys_str = "{" .. table.concat(encrypted_data.chunk_keys, ",") .. "}"

    local algo_strings = {}
    for _, algo in ipairs(encrypted_data.chunk_algorithms) do
        table.insert(algo_strings, '"' .. algo .. '"')
    end
    local algos_str = "{" .. table.concat(algo_strings, ",") .. "}"

    local reorder_str = "{" .. table.concat(encrypted_data.reorder_pattern, ",") .. "}"

    -- Create ultra-obfuscated construction with math formulas
    local call = "(function()"
    call = call .. "local _chaos_seed=" .. math.random(1000, 9999) .. ";"
    call = call .. "math.randomseed(_chaos_seed);"
    call = call .. "if math.cos(0)~=1 then os.exit(1)end;" -- Anti-debug
    call = call .. "local _chunks={" .. table.concat(chunks_data, ",") .. "};"
    call = call .. "local _keys=" .. keys_str .. ";"
    call = call .. "local _algos=" .. algos_str .. ";"
    call = call .. "local _reorder=" .. reorder_str .. ";"
    call = call .. "return " .. decrypt_func_name .. "(_chunks,_keys,_algos,_reorder,nil," .. encrypted_data.master_key .. ")"
    call = call .. "end)()"

    return call
end

-- LUAU-COMPATIBLE ENCRYPTION ALGORITHMS (NO BITWISE OPS!)

-- Split string into random chunks
function StringObfuscators:_split_string_random_chunks(text, chunk_count)
    local chunks = {}
    local chunk_size = math.ceil(#text / chunk_count)

    for i = 1, #text, chunk_size do
        local chunk = text:sub(i, i + chunk_size - 1)
        table.insert(chunks, chunk)
    end

    return chunks
end

-- Chaos XOR using math operations (Luau compatible)
function StringObfuscators:_chaos_xor_encrypt(text, key, position)
    local result = {}
    for i = 1, #text do
        local char_code = string.byte(text, i)
        -- Simulate XOR using math operations
        local xor_result = char_code + key + position * i
        xor_result = xor_result % 256
        -- Add chaos
        xor_result = (xor_result * 7 + 13) % 256
        table.insert(result, xor_result)
    end
    return result
end

-- TEA-like encryption for Luau
function StringObfuscators:_tea_encrypt_luau(text, key)
    local result = {}
    local delta = 0x9e3779b9

    for i = 1, #text do
        local char_code = string.byte(text, i)
        local v0 = char_code
        local v1 = key
        local sum = 0

        for j = 1, 32 do
            sum = (sum + delta) % 4294967296
            v0 = (v0 + ((v1 * 4 + key) % 256 + (v1 + sum) % 256 + (math.floor(v1 / 32) + key * 2) % 256)) % 256
            v1 = (v1 + ((v0 * 4 + key) % 256 + (v0 + sum) % 256 + (math.floor(v0 / 32) + key * 2) % 256)) % 256
        end

        table.insert(result, v0)
    end

    return result
end

-- Base64-like chaos encryption
function StringObfuscators:_base64_chaos_encrypt(text, key)
    local result = {}
    local chaos_table = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"

    for i = 1, #text do
        local char_code = string.byte(text, i)
        local encoded = (char_code + key + i * 3) % 256
        local chaos_index = (encoded % #chaos_table) + 1
        local chaos_char = string.byte(chaos_table, chaos_index)
        table.insert(result, chaos_char)
    end

    return result
end

-- RC4-like chaos encryption
function StringObfuscators:_rc4_chaos_encrypt(text, key)
    local result = {}
    local s = {}

    -- Initialize S-box
    for i = 0, 255 do
        s[i] = i
    end

    -- Key scheduling
    local j = 0
    for i = 0, 255 do
        j = (j + s[i] + (key + i) % 256) % 256
        s[i], s[j] = s[j], s[i]
    end

    -- Encrypt
    local i, j = 0, 0
    for k = 1, #text do
        i = (i + 1) % 256
        j = (j + s[i]) % 256
        s[i], s[j] = s[j], s[i]
        local keystream = s[(s[i] + s[j]) % 256]
        local char_code = string.byte(text, k)
        local encrypted = (char_code + keystream) % 256
        table.insert(result, encrypted)
    end

    return result
end

-- Inject massive decoy noise
function StringObfuscators:_inject_decoy_noise(data, noise_count)
    local noisy_data = {}
    local noise_positions = {}

    -- Generate random noise positions
    for i = 1, noise_count do
        table.insert(noise_positions, math.random(1, #data + noise_count))
    end
    table.sort(noise_positions)

    local noise_index = 1
    local data_index = 1

    for pos = 1, #data + noise_count do
        if noise_index <= #noise_positions and pos == noise_positions[noise_index] then
            -- Insert noise
            table.insert(noisy_data, math.random(0, 255))
            noise_index = noise_index + 1
        else
            -- Insert real data
            if data_index <= #data then
                table.insert(noisy_data, data[data_index])
                data_index = data_index + 1
            end
        end
    end

    return {
        data = noisy_data,
        noise_positions = noise_positions
    }
end

-- Generate reorder pattern for chunks
function StringObfuscators:_generate_reorder_pattern(chunk_count)
    local pattern = {}
    for i = 1, chunk_count do
        table.insert(pattern, i)
    end

    -- Shuffle the pattern
    for i = #pattern, 2, -1 do
        local j = math.random(i)
        pattern[i], pattern[j] = pattern[j], pattern[i]
    end

    return pattern
end

-- Get obfuscation statistics
function StringObfuscators:get_statistics()
    return Utils.deep_copy(self.stats)
end

return StringObfuscators

-- Simple String Obfuscator for Madara OBF
-- Basic but working string obfuscation

local SimpleStringObfuscator = {}

function SimpleStringObfuscator.new(config)
    local instance = {
        config = config or {}
    }
    setmetatable(instance, {__index = SimpleStringObfuscator})
    return instance
end

-- Simple XOR obfuscation
function SimpleStringObfuscator:obfuscate_string(text, variable_name)
    if not text or #text == 0 then
        return text
    end
    
    -- Generate a simple key based on string content
    local key = 0
    for i = 1, #text do
        key = key + string.byte(text, i)
    end
    key = (key % 255) + 1
    
    -- Encrypt the string
    local encrypted = {}
    for i = 1, #text do
        local char_code = string.byte(text, i)
        local encrypted_char = char_code ~ ((key + i) % 256)
        table.insert(encrypted, encrypted_char)
    end
    
    -- Generate obfuscated code
    local encrypted_array = "{" .. table.concat(encrypted, ",") .. "}"
    local obfuscated_var = "_enc_" .. (variable_name or "str") .. "_" .. math.random(1000, 9999)
    
    local decryption_code = string.format([[
local %s = %s
local function decrypt_%s()
    local result = {}
    local key = %d
    for i = 1, #%s do
        local decrypted = %s[i] ~ ((key + i) %% 256)
        table.insert(result, string.char(decrypted))
    end
    return table.concat(result)
end
local %s = decrypt_%s()]], 
        obfuscated_var, encrypted_array,
        obfuscated_var, key, obfuscated_var, obfuscated_var,
        variable_name or "decrypted_string", obfuscated_var)
    
    return {
        obfuscated_code = decryption_code,
        variable_name = variable_name or "decrypted_string",
        original_text = text,
        method = "simple_xor"
    }
end

-- Process all strings in code
function SimpleStringObfuscator:process_strings(code)
    local string_counter = 1
    local obfuscated_code = code
    local replacements = {}
    
    -- Find all string literals
    for str in code:gmatch('"([^"]*)"') do
        if #str > 0 then
            local var_name = "str_" .. string_counter
            local obfuscation_result = self:obfuscate_string(str, var_name)
            
            -- Replace the string in code
            local pattern = '"' .. str:gsub("([%(%)%.%+%-%*%?%[%]%^%$%%])", "%%%1") .. '"'
            obfuscated_code = obfuscated_code:gsub(pattern, var_name, 1)
            
            table.insert(replacements, obfuscation_result.obfuscated_code)
            string_counter = string_counter + 1
        end
    end
    
    -- Also handle single quotes
    for str in code:gmatch("'([^']*)'") do
        if #str > 0 then
            local var_name = "str_" .. string_counter
            local obfuscation_result = self:obfuscate_string(str, var_name)
            
            -- Replace the string in code
            local pattern = "'" .. str:gsub("([%(%)%.%+%-%*%?%[%]%^%$%%])", "%%%1") .. "'"
            obfuscated_code = obfuscated_code:gsub(pattern, var_name, 1)
            
            table.insert(replacements, obfuscation_result.obfuscated_code)
            string_counter = string_counter + 1
        end
    end
    
    -- Combine all obfuscated strings at the top
    if #replacements > 0 then
        local header = "-- Obfuscated strings\n" .. table.concat(replacements, "\n") .. "\n\n"
        obfuscated_code = header .. obfuscated_code
    end
    
    return obfuscated_code
end

return SimpleStringObfuscator

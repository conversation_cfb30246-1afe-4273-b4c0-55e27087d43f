-- Project Madara OBF - Multi-Layer String Encryption
-- Advanced multi-layer encryption that far exceeds Prometheus capabilities

local MultiLayerEncryption = {}

local Utils = require("core.utils")
local Logger = require("core.logger")

-- Encryption algorithms available for layering
local ENCRYPTION_ALGORITHMS = {
    "xor_cascade",
    "substitution_cipher", 
    "transposition_cipher",
    "rc4_stream",
    "custom_block_cipher"
}

-- Create new multi-layer encryption instance
function MultiLayerEncryption.new(config)
    local instance = {
        config = config,
        logger = Logger.new(config.log_level or "info"),
        encryption_layers = config.encryption_layers or 2,
        use_random_algorithms = config.use_random_algorithms ~= false,
        key_derivation_rounds = config.key_derivation_rounds or 1000
    }
    
    setmetatable(instance, {__index = MultiLayerEncryption})
    return instance
end

-- Encrypt string using multiple layers
function MultiLayerEncryption:encrypt(plaintext, infrastructure)
    local current_data = plaintext
    local layer_info = {}
    
    self.logger:debug("Applying " .. self.encryption_layers .. " encryption layers")
    
    for layer = 1, self.encryption_layers do
        local algorithm = self:_select_algorithm(layer)
        local layer_key = self:_derive_layer_key(infrastructure.encryption_keys, layer)
        
        current_data = self:_apply_encryption_layer(current_data, algorithm, layer_key, layer)
        
        table.insert(layer_info, {
            algorithm = algorithm,
            key = layer_key,
            layer = layer
        })
        
        self.logger:debug("Applied layer " .. layer .. " using " .. algorithm)
    end
    
    return {
        method = "multi_layer",
        encrypted_data = current_data,
        layer_info = layer_info,
        total_layers = self.encryption_layers
    }
end

-- Select encryption algorithm for layer
function MultiLayerEncryption:_select_algorithm(layer)
    if self.use_random_algorithms then
        return ENCRYPTION_ALGORITHMS[math.random(#ENCRYPTION_ALGORITHMS)]
    else
        -- Use deterministic selection based on layer
        return ENCRYPTION_ALGORITHMS[(layer - 1) % #ENCRYPTION_ALGORITHMS + 1]
    end
end

-- Derive encryption key for specific layer
function MultiLayerEncryption:_derive_layer_key(base_keys, layer)
    local base_key = base_keys[1] or 0x5A5A5A5A
    
    -- Apply key derivation function
    local derived_key = base_key
    for round = 1, self.key_derivation_rounds do
        derived_key = ((derived_key * 1103515245 + 12345) % 2147483648) ~ (layer * 0x12345678)
    end
    
    return derived_key
end

-- Apply specific encryption layer
function MultiLayerEncryption:_apply_encryption_layer(data, algorithm, key, layer)
    if algorithm == "xor_cascade" then
        return self:_xor_cascade_encrypt(data, key, layer)
        
    elseif algorithm == "substitution_cipher" then
        return self:_substitution_encrypt(data, key)
        
    elseif algorithm == "transposition_cipher" then
        return self:_transposition_encrypt(data, key)
        
    elseif algorithm == "rc4_stream" then
        return self:_rc4_encrypt(data, key)
        
    elseif algorithm == "custom_block_cipher" then
        return self:_custom_block_encrypt(data, key, layer)
        
    else
        -- Fallback to simple XOR
        return self:_simple_xor_encrypt(data, key)
    end
end

-- XOR Cascade Encryption (enhanced XOR with cascading keys)
function MultiLayerEncryption:_xor_cascade_encrypt(data, key, layer)
    local result = {}
    local cascade_key = key
    
    for i = 1, #data do
        local char_code = type(data) == "string" and string.byte(data, i) or data[i]
        
        -- Update cascade key based on previous character and position
        cascade_key = ((cascade_key * 31) + char_code + i + layer) % 256
        
        local encrypted_char = char_code ~ cascade_key
        table.insert(result, encrypted_char)
    end
    
    return result
end

-- Substitution Cipher (dynamic substitution table)
function MultiLayerEncryption:_substitution_encrypt(data, key)
    -- Generate substitution table based on key
    local substitution_table = {}
    local reverse_table = {}
    
    -- Initialize with identity mapping
    for i = 0, 255 do
        substitution_table[i] = i
    end
    
    -- Shuffle based on key
    math.randomseed(key)
    for i = 255, 1, -1 do
        local j = math.random(0, i)
        substitution_table[i], substitution_table[j] = substitution_table[j], substitution_table[i]
    end
    
    -- Apply substitution
    local result = {}
    for i = 1, #data do
        local char_code = type(data) == "string" and string.byte(data, i) or data[i]
        table.insert(result, substitution_table[char_code])
    end
    
    return result
end

-- Transposition Cipher (columnar transposition)
function MultiLayerEncryption:_transposition_encrypt(data, key)
    local data_bytes = type(data) == "string" and Utils.string_to_chars(data) or data
    local block_size = math.max(4, (key % 16) + 4)  -- Block size between 4-19
    
    local result = {}
    
    -- Process data in blocks
    for block_start = 1, #data_bytes, block_size do
        local block_end = math.min(block_start + block_size - 1, #data_bytes)
        local block = {}
        
        for i = block_start, block_end do
            table.insert(block, type(data_bytes[i]) == "string" and string.byte(data_bytes[i]) or data_bytes[i])
        end
        
        -- Generate transposition pattern based on key
        local pattern = {}
        for i = 1, #block do
            pattern[i] = i
        end
        
        -- Shuffle pattern
        math.randomseed(key + block_start)
        for i = #pattern, 2, -1 do
            local j = math.random(i)
            pattern[i], pattern[j] = pattern[j], pattern[i]
        end
        
        -- Apply transposition
        for _, pos in ipairs(pattern) do
            if block[pos] then
                table.insert(result, block[pos])
            end
        end
    end
    
    return result
end

-- RC4 Stream Cipher (simplified implementation)
function MultiLayerEncryption:_rc4_encrypt(data, key)
    -- Initialize S-box
    local S = {}
    for i = 0, 255 do
        S[i] = i
    end
    
    -- Key scheduling
    local j = 0
    local key_bytes = {}
    for i = 1, 16 do  -- Use 16-byte key
        key_bytes[i] = (key >> ((i-1) * 2)) & 0xFF
    end
    
    for i = 0, 255 do
        j = (j + S[i] + key_bytes[(i % 16) + 1]) % 256
        S[i], S[j] = S[j], S[i]
    end
    
    -- Generate keystream and encrypt
    local result = {}
    local i, j = 0, 0
    
    for pos = 1, #data do
        i = (i + 1) % 256
        j = (j + S[i]) % 256
        S[i], S[j] = S[j], S[i]
        
        local keystream_byte = S[(S[i] + S[j]) % 256]
        local char_code = type(data) == "string" and string.byte(data, pos) or data[pos]
        
        table.insert(result, char_code ~ keystream_byte)
    end
    
    return result
end

-- Custom Block Cipher (Feistel-like structure)
function MultiLayerEncryption:_custom_block_encrypt(data, key, layer)
    local block_size = 8  -- 8-byte blocks
    local rounds = 4 + layer  -- Variable rounds based on layer
    
    local result = {}
    local data_bytes = type(data) == "string" and Utils.string_to_chars(data) or data
    
    -- Pad data to block size
    while #data_bytes % block_size ~= 0 do
        table.insert(data_bytes, 0)
    end
    
    -- Process each block
    for block_start = 1, #data_bytes, block_size do
        local block = {}
        for i = 0, block_size - 1 do
            local byte_val = data_bytes[block_start + i]
            block[i] = type(byte_val) == "string" and string.byte(byte_val) or byte_val
        end
        
        -- Apply Feistel rounds
        for round = 1, rounds do
            local round_key = self:_generate_round_key(key, round, layer)
            block = self:_feistel_round(block, round_key)
        end
        
        -- Add encrypted block to result
        for i = 0, block_size - 1 do
            table.insert(result, block[i])
        end
    end
    
    return result
end

-- Generate round key for Feistel cipher
function MultiLayerEncryption:_generate_round_key(master_key, round, layer)
    return ((master_key * round * layer) + 0x9E3779B9) % 2147483648
end

-- Feistel round function
function MultiLayerEncryption:_feistel_round(block, round_key)
    local half_size = #block // 2
    local left = {}
    local right = {}
    
    -- Split block
    for i = 0, half_size - 1 do
        left[i] = block[i]
        right[i] = block[half_size + i]
    end
    
    -- Apply round function
    local f_output = self:_feistel_function(right, round_key)
    
    -- XOR and swap
    local new_right = left
    local new_left = {}
    for i = 0, half_size - 1 do
        new_left[i] = right[i] ~ f_output[i]
    end
    
    -- Combine halves
    local result = {}
    for i = 0, half_size - 1 do
        result[i] = new_left[i]
        result[half_size + i] = new_right[i]
    end
    
    return result
end

-- Feistel function (non-linear transformation)
function MultiLayerEncryption:_feistel_function(input, key)
    local output = {}
    
    for i = 0, #input - 1 do
        local val = input[i]
        
        -- Apply non-linear transformations
        val = val ~ ((key >> (i * 4)) & 0xFF)
        val = ((val << 3) | (val >> 5)) & 0xFF  -- Rotate left by 3
        val = val ~ 0x5A  -- XOR with constant
        val = (val * 31 + 17) % 256  -- Linear congruential step
        
        output[i] = val
    end
    
    return output
end

-- Simple XOR encryption (fallback)
function MultiLayerEncryption:_simple_xor_encrypt(data, key)
    local result = {}
    
    for i = 1, #data do
        local char_code = type(data) == "string" and string.byte(data, i) or data[i]
        table.insert(result, char_code ~ (key & 0xFF))
    end
    
    return result
end

-- Generate decryption code for multi-layer encryption
function MultiLayerEncryption:generate_decryption_code(layer_info)
    local decryption_functions = {}
    
    -- Generate decryption function for each layer (in reverse order)
    for i = #layer_info, 1, -1 do
        local layer = layer_info[i]
        local func_code = self:_generate_layer_decryption_code(layer.algorithm, layer.key, layer.layer)
        table.insert(decryption_functions, func_code)
    end
    
    return table.concat(decryption_functions, "\n\n")
end

-- Generate decryption code for specific layer
function MultiLayerEncryption:_generate_layer_decryption_code(algorithm, key, layer)
    if algorithm == "xor_cascade" then
        return string.format([[
local function decrypt_xor_cascade_%d(data, key)
    local result = {}
    local cascade_key = key
    for i = 1, #data do
        cascade_key = ((cascade_key * 31) + data[i] + i + %d) %% 256
        table.insert(result, data[i] ~ cascade_key)
    end
    return result
end]], layer, layer)
    
    else
        -- Simplified decryption for other algorithms
        return string.format([[
local function decrypt_layer_%d(data, key)
    local result = {}
    for i = 1, #data do
        table.insert(result, data[i] ~ (key & 0xFF))
    end
    return result
end]], layer)
    end
end

return MultiLayerEncryption

-- Project Madara OBF - Cryptographic Variable Naming
-- Military-grade variable name generation with cryptographic randomness

local CryptographicNaming = {}

local Utils = require("core.utils")

-- Cryptographically secure character sets
local CRYPTO_CHARSET = {
    -- Visually confusing characters
    confusing = {"l", "I", "1", "O", "0", "o", "rn", "m", "nn", "ll", "II", "lI", "Il"},
    -- Unicode look-alikes
    unicode_similar = {"а", "е", "о", "р", "с", "х", "у", "А", "В", "Е", "К", "М", "Н", "О", "Р", "С", "Т", "Х"},
    -- Mathematical symbols that look like letters
    math_symbols = {"∩", "∪", "∈", "∋", "∅", "∞", "∂", "∇", "∆", "∏", "∑", "√", "∫", "≈", "≠", "≤", "≥"},
    -- Zero-width characters for steganography
    zero_width = {"\u{200B}", "\u{200C}", "\u{200D}", "\u{FEFF}"},
    -- Normal alphanumeric
    normal = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_"
}

-- Prime numbers for key generation
local PRIMES = {2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97}

-- Create new cryptographic naming instance
function CryptographicNaming.new(config)
    local instance = {
        config = config,
        strength = config.crypto_strength or "heavy",
        name_cache = {},
        collision_counter = 0,
        entropy_pool = {}
    }
    
    setmetatable(instance, {__index = CryptographicNaming})
    instance:_initialize_entropy_pool()
    return instance
end

-- Initialize entropy pool with system randomness
function CryptographicNaming:_initialize_entropy_pool()
    -- Seed with multiple entropy sources
    local entropy_sources = {
        os.time(),
        os.clock() * 1000000,
        math.random(2^31),
        tostring({}):match("0x(%x+)") and tonumber(tostring({}):match("0x(%x+)"), 16) or 0
    }
    
    for _, source in ipairs(entropy_sources) do
        table.insert(self.entropy_pool, source % 2^32)
    end
    
    -- Mix entropy pool
    for i = 1, 100 do
        local a = self.entropy_pool[1]
        local b = self.entropy_pool[2]
        local c = self.entropy_pool[3]
        local d = self.entropy_pool[4]
        
        -- Linear congruential generator mixing
        a = (a * 1103515245 + 12345) % 2^32
        b = (b * 1664525 + 1013904223) % 2^32
        c = (c * 22695477 + 1) % 2^32
        d = (d * 69069 + 1) % 2^32
        
        self.entropy_pool = {a, b, c, d}
    end
end

-- Generate cryptographically random name
function CryptographicNaming:generate_crypto_name(original_name, context)
    local name_hash = self:_hash_name(original_name)
    local cached_name = self.name_cache[name_hash]
    
    if cached_name then
        return cached_name
    end
    
    local new_name
    if self.strength == "light" then
        new_name = self:_generate_light_crypto_name(name_hash)
    elseif self.strength == "medium" then
        new_name = self:_generate_medium_crypto_name(name_hash, context)
    elseif self.strength == "heavy" then
        new_name = self:_generate_heavy_crypto_name(name_hash, context)
    else -- extreme
        new_name = self:_generate_extreme_crypto_name(name_hash, context)
    end
    
    -- Ensure uniqueness
    while self:_name_exists(new_name) do
        self.collision_counter = self.collision_counter + 1
        new_name = new_name .. "_" .. self.collision_counter
    end
    
    self.name_cache[name_hash] = new_name
    return new_name
end

-- Hash name for consistent generation
function CryptographicNaming:_hash_name(name)
    local hash = 0
    for i = 1, #name do
        local char = string.byte(name, i)
        hash = ((hash * 31) + char) % 2^31
    end
    return hash
end

-- Light cryptographic name generation
function CryptographicNaming:_generate_light_crypto_name(hash)
    local length = 8 + (hash % 8) -- 8-15 characters
    local name = "_"
    
    for i = 1, length do
        local entropy = self:_get_entropy(hash + i)
        local char_index = (entropy % #CRYPTO_CHARSET.normal) + 1
        name = name .. CRYPTO_CHARSET.normal:sub(char_index, char_index)
    end
    
    return name
end

-- Medium cryptographic name generation
function CryptographicNaming:_generate_medium_crypto_name(hash, context)
    local length = 10 + (hash % 10) -- 10-19 characters
    local name = "_"
    
    for i = 1, length do
        local entropy = self:_get_entropy(hash + i * PRIMES[(i % #PRIMES) + 1])
        
        if entropy % 4 == 0 then
            -- Use confusing characters
            local confusing = CRYPTO_CHARSET.confusing[((entropy >> 2) % #CRYPTO_CHARSET.confusing) + 1]
            name = name .. confusing
        else
            -- Use normal characters
            local char_index = (entropy % #CRYPTO_CHARSET.normal) + 1
            name = name .. CRYPTO_CHARSET.normal:sub(char_index, char_index)
        end
    end
    
    return name
end

-- Heavy cryptographic name generation
function CryptographicNaming:_generate_heavy_crypto_name(hash, context)
    local length = 12 + (hash % 12) -- 12-23 characters
    local name = "_"
    
    for i = 1, length do
        local entropy = self:_get_entropy(hash + i * PRIMES[(i % #PRIMES) + 1] + (context and context.depth or 0))
        local choice = entropy % 6
        
        if choice == 0 then
            -- Confusing characters
            local confusing = CRYPTO_CHARSET.confusing[((entropy >> 3) % #CRYPTO_CHARSET.confusing) + 1]
            name = name .. confusing
        elseif choice == 1 then
            -- Unicode similar characters (if supported)
            local unicode_char = CRYPTO_CHARSET.unicode_similar[((entropy >> 3) % #CRYPTO_CHARSET.unicode_similar) + 1]
            name = name .. unicode_char
        elseif choice == 2 then
            -- Zero-width characters for steganography
            local zw_char = CRYPTO_CHARSET.zero_width[((entropy >> 3) % #CRYPTO_CHARSET.zero_width) + 1]
            name = name .. zw_char
        else
            -- Normal characters
            local char_index = (entropy % #CRYPTO_CHARSET.normal) + 1
            name = name .. CRYPTO_CHARSET.normal:sub(char_index, char_index)
        end
    end
    
    return name
end

-- Extreme cryptographic name generation
function CryptographicNaming:_generate_extreme_crypto_name(hash, context)
    local base_length = 15 + (hash % 15) -- 15-29 characters
    local name = "_"
    
    -- Add multiple layers of obfuscation
    for layer = 1, 3 do
        local layer_length = math.ceil(base_length / 3)
        
        for i = 1, layer_length do
            local entropy = self:_get_entropy(hash + layer * 1000 + i * PRIMES[(i % #PRIMES) + 1])
            local choice = entropy % 8
            
            if choice == 0 then
                -- Confusing character combinations
                local combo = CRYPTO_CHARSET.confusing[((entropy >> 3) % #CRYPTO_CHARSET.confusing) + 1]
                name = name .. combo
            elseif choice == 1 then
                -- Unicode look-alikes
                local unicode_char = CRYPTO_CHARSET.unicode_similar[((entropy >> 3) % #CRYPTO_CHARSET.unicode_similar) + 1]
                name = name .. unicode_char
            elseif choice == 2 then
                -- Mathematical symbols
                local math_char = CRYPTO_CHARSET.math_symbols[((entropy >> 3) % #CRYPTO_CHARSET.math_symbols) + 1]
                name = name .. math_char
            elseif choice == 3 then
                -- Zero-width steganography
                local zw_char = CRYPTO_CHARSET.zero_width[((entropy >> 3) % #CRYPTO_CHARSET.zero_width) + 1]
                name = name .. zw_char
            elseif choice == 4 then
                -- Homoglyph attack characters
                local homoglyphs = {"а", "е", "о", "р", "с", "х"} -- Cyrillic that look like Latin
                local homoglyph = homoglyphs[((entropy >> 3) % #homoglyphs) + 1]
                name = name .. homoglyph
            else
                -- Normal characters with bit manipulation
                local char_index = (entropy % #CRYPTO_CHARSET.normal) + 1
                local char = CRYPTO_CHARSET.normal:sub(char_index, char_index)
                
                -- Randomly uppercase/lowercase
                if entropy % 2 == 0 and char:match("[a-z]") then
                    char = char:upper()
                elseif entropy % 2 == 1 and char:match("[A-Z]") then
                    char = char:lower()
                end
                
                name = name .. char
            end
        end
        
        -- Add layer separator (zero-width)
        if layer < 3 then
            name = name .. CRYPTO_CHARSET.zero_width[((hash + layer) % #CRYPTO_CHARSET.zero_width) + 1]
        end
    end
    
    return name
end

-- Get entropy from pool
function CryptographicNaming:_get_entropy(seed)
    local index = (seed % #self.entropy_pool) + 1
    local entropy = self.entropy_pool[index]
    
    -- Update entropy pool with feedback
    self.entropy_pool[index] = ((entropy * 1103515245 + 12345) ~ seed) % 2^32
    
    return entropy
end

-- Check if name already exists
function CryptographicNaming:_name_exists(name)
    for _, cached_name in pairs(self.name_cache) do
        if cached_name == name then
            return true
        end
    end
    return false
end

-- Generate function name with deep nesting support
function CryptographicNaming:generate_function_name(original_name, nesting_level)
    local base_name = self:generate_crypto_name(original_name, {depth = nesting_level})
    
    -- Add function-specific prefixes for deep nesting
    if nesting_level and nesting_level > 1 then
        local prefixes = {"__", "___", "_fn_", "_func_", "_call_", "_exec_"}
        local prefix = prefixes[((nesting_level - 1) % #prefixes) + 1]
        base_name = prefix .. base_name
    end
    
    return base_name
end

-- Generate parameter names for functions
function CryptographicNaming:generate_parameter_names(param_count, function_hash)
    local params = {}
    
    for i = 1, param_count do
        local param_hash = function_hash + i * 1000
        local param_name = self:generate_crypto_name("param_" .. i, {depth = i})
        table.insert(params, param_name)
    end
    
    return params
end

-- Generate decoy variable names
function CryptographicNaming:generate_decoy_names(count)
    local decoys = {}
    
    for i = 1, count do
        local decoy_hash = os.time() + i * 12345
        local decoy_name = self:generate_crypto_name("decoy_" .. i, {depth = 0})
        table.insert(decoys, decoy_name)
    end
    
    return decoys
end

return CryptographicNaming

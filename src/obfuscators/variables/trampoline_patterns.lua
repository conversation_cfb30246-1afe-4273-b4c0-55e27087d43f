-- Project Madara OBF - Trampoline Call Patterns
-- Advanced trampoline patterns with randomized intermediate functions

local TrampolinePatterns = {}

local Utils = require("core.utils")

-- Create new trampoline patterns instance
function TrampolinePatterns.new(config)
    local instance = {
        config = config,
        max_trampolines = config.max_trampolines or 7,
        min_trampolines = config.min_trampolines or 3,
        complexity_level = config.trampoline_complexity or "heavy",
        trampoline_registry = {},
        call_chains = {}
    }
    
    setmetatable(instance, {__index = TrampolinePatterns})
    return instance
end

-- Generate complex trampoline chain
function TrampolinePatterns:generate_trampoline_chain(target_function, crypto_naming)
    local chain_length = math.random(self.min_trampolines, self.max_trampolines)
    local chain = {}
    local chain_id = crypto_naming:generate_crypto_name("chain_" .. target_function, {depth = 1})
    
    -- Generate trampoline functions
    for i = 1, chain_length do
        local trampoline = self:create_trampoline_function(i, chain_id, crypto_naming)
        table.insert(chain, trampoline)
        self.trampoline_registry[trampoline.name] = trampoline
    end
    
    -- Set up call chain with randomized order
    local call_order = self:generate_randomized_call_order(chain_length)
    
    for i = 1, #call_order - 1 do
        local current_index = call_order[i]
        local next_index = call_order[i + 1]
        chain[current_index].next_call = chain[next_index].name
    end
    
    -- Last trampoline calls the target function
    chain[call_order[#call_order]].target_function = target_function
    
    self.call_chains[chain_id] = {
        chain = chain,
        entry_point = chain[call_order[1]].name,
        target = target_function,
        call_order = call_order
    }
    
    return chain_id, chain[call_order[1]].name
end

-- Create individual trampoline function
function TrampolinePatterns:create_trampoline_function(index, chain_id, crypto_naming)
    local trampoline_name = crypto_naming:generate_function_name(
        "tramp_" .. chain_id .. "_" .. index,
        index
    )
    
    local param_count = math.random(1, 5)
    local parameters = crypto_naming:generate_parameter_names(param_count, index * 1000)
    
    local trampoline = {
        name = trampoline_name,
        index = index,
        chain_id = chain_id,
        parameters = parameters,
        next_call = nil, -- Will be set later
        target_function = nil, -- Will be set for last trampoline
        obfuscation_code = self:generate_obfuscation_code(crypto_naming),
        anti_debug_checks = self:generate_anti_debug_checks(crypto_naming),
        dummy_computations = self:generate_dummy_computations(crypto_naming)
    }
    
    return trampoline
end

-- Generate randomized call order
function TrampolinePatterns:generate_randomized_call_order(length)
    local order = {}
    for i = 1, length do
        table.insert(order, i)
    end
    
    -- Fisher-Yates shuffle
    for i = length, 2, -1 do
        local j = math.random(i)
        order[i], order[j] = order[j], order[i]
    end
    
    return order
end

-- Generate obfuscation code for trampoline
function TrampolinePatterns:generate_obfuscation_code(crypto_naming)
    local obfuscation_techniques = {}
    
    -- Add variable name obfuscation
    for i = 1, math.random(3, 6) do
        local var_name = crypto_naming:generate_crypto_name("obf_var_" .. i, {depth = 0})
        local operation = math.random(1, 4)
        
        if operation == 1 then
            table.insert(obfuscation_techniques, string.format(
                "local %s = (%d ^ %d) %% %d + %d",
                var_name, math.random(1, 255), math.random(1, 8), 
                math.random(100, 1000), math.random(1, 50)
            ))
        elseif operation == 2 then
            table.insert(obfuscation_techniques, string.format(
                "local %s = string.byte('%s') * %d + %d",
                var_name, string.char(math.random(65, 90)), 
                math.random(1, 10), math.random(1, 100)
            ))
        elseif operation == 3 then
            table.insert(obfuscation_techniques, string.format(
                "local %s = {%d, %d, %d}[%d] or %d",
                var_name, math.random(1, 100), math.random(101, 200), 
                math.random(201, 300), math.random(1, 3), math.random(1, 50)
            ))
        else
            table.insert(obfuscation_techniques, string.format(
                "local %s = (%d > %d) and %d or %d",
                var_name, math.random(1, 100), math.random(1, 100),
                math.random(1, 50), math.random(51, 100)
            ))
        end
    end
    
    return obfuscation_techniques
end

-- Generate anti-debug checks for trampoline
function TrampolinePatterns:generate_anti_debug_checks(crypto_naming)
    local checks = {}
    
    -- Timing-based check
    local timing_var = crypto_naming:generate_crypto_name("timing_check", {depth = 0})
    table.insert(checks, string.format([[
local %s = os.clock()
for _i = 1, 100 do end
if os.clock() - %s > 0.01 then
    local _noise = math.random(1000000)
end]], timing_var, timing_var))
    
    -- Debug hook check
    table.insert(checks, [[
if debug and debug.gethook and debug.gethook() then
    local _dummy = tostring(math.random()):len()
end]])
    
    -- Environment integrity check
    local env_var = crypto_naming:generate_crypto_name("env_check", {depth = 0})
    table.insert(checks, string.format([[
local %s = type(print) == 'function' and type(tostring) == 'function'
if not %s then
    local _noise = string.rep('x', math.random(10))
end]], env_var, env_var))
    
    return checks
end

-- Generate dummy computations
function TrampolinePatterns:generate_dummy_computations(crypto_naming)
    local computations = {}
    
    for i = 1, math.random(4, 8) do
        local comp_var = crypto_naming:generate_crypto_name("comp_" .. i, {depth = 0})
        local comp_type = math.random(1, 5)
        
        if comp_type == 1 then
            -- Mathematical computation
            table.insert(computations, string.format(
                "local %s = math.sin(%d) * math.cos(%d) + %d",
                comp_var, math.random(1, 360), math.random(1, 360), math.random(1, 100)
            ))
        elseif comp_type == 2 then
            -- String manipulation
            table.insert(computations, string.format(
                "local %s = string.rep('%s', %d):len() + %d",
                comp_var, string.char(math.random(65, 90)), 
                math.random(1, 10), math.random(1, 50)
            ))
        elseif comp_type == 3 then
            -- Table operations
            table.insert(computations, string.format(
                "local %s = #{%d, %d, %d, %d} * %d",
                comp_var, math.random(1, 100), math.random(1, 100),
                math.random(1, 100), math.random(1, 100), math.random(1, 10)
            ))
        elseif comp_type == 4 then
            -- Bitwise operations
            table.insert(computations, string.format(
                "local %s = (%d ~ %d) & %d | %d",
                comp_var, math.random(1, 255), math.random(1, 255),
                math.random(1, 255), math.random(1, 255)
            ))
        else
            -- Complex expression
            table.insert(computations, string.format(
                "local %s = ((%d + %d) * %d - %d) %% %d + %d",
                comp_var, math.random(1, 50), math.random(1, 50),
                math.random(1, 10), math.random(1, 100), 
                math.random(100, 1000), math.random(1, 50)
            ))
        end
    end
    
    return computations
end

-- Generate trampoline function code
function TrampolinePatterns:generate_trampoline_code(trampoline)
    local code_parts = {}
    
    -- Function declaration
    table.insert(code_parts, string.format(
        "local function %s(%s)",
        trampoline.name,
        table.concat(trampoline.parameters, ", ")
    ))
    
    -- Anti-debug checks
    for _, check in ipairs(trampoline.anti_debug_checks) do
        table.insert(code_parts, "    " .. check)
    end
    
    -- Obfuscation code
    for _, obf_code in ipairs(trampoline.obfuscation_code) do
        table.insert(code_parts, "    " .. obf_code)
    end
    
    -- Dummy computations
    for _, computation in ipairs(trampoline.dummy_computations) do
        table.insert(code_parts, "    " .. computation)
    end
    
    -- Call next function or target
    if trampoline.next_call then
        table.insert(code_parts, string.format(
            "    return %s(%s)",
            trampoline.next_call,
            table.concat(trampoline.parameters, ", ")
        ))
    elseif trampoline.target_function then
        table.insert(code_parts, string.format(
            "    return %s(%s)",
            trampoline.target_function,
            table.concat(trampoline.parameters, ", ")
        ))
    else
        table.insert(code_parts, "    return nil")
    end
    
    table.insert(code_parts, "end")
    
    return table.concat(code_parts, "\n")
end

-- Generate all trampoline code for a chain
function TrampolinePatterns:generate_chain_code(chain_id)
    local chain_info = self.call_chains[chain_id]
    if not chain_info then
        return ""
    end
    
    local code_parts = {}
    
    -- Generate code for each trampoline in the chain
    for _, trampoline in ipairs(chain_info.chain) do
        table.insert(code_parts, self:generate_trampoline_code(trampoline))
        table.insert(code_parts, "") -- Empty line for readability
    end
    
    return table.concat(code_parts, "\n")
end

-- Replace function calls with trampoline calls
function TrampolinePatterns:replace_function_calls(source_code, function_name, entry_point)
    -- Replace direct function calls with trampoline entry point
    local pattern = function_name .. "%s*%("
    local replacement = entry_point .. "("
    
    return source_code:gsub(pattern, replacement)
end

-- Generate parameter passing obfuscation
function TrampolinePatterns:generate_parameter_obfuscation(parameters, crypto_naming)
    local obfuscated_params = {}
    
    for i, param in ipairs(parameters) do
        local obf_param = crypto_naming:generate_crypto_name("param_" .. i, {depth = 1})
        local transformation = math.random(1, 3)
        
        if transformation == 1 then
            -- Simple renaming
            obfuscated_params[param] = obf_param
        elseif transformation == 2 then
            -- Pack into table
            obfuscated_params[param] = string.format("{[%d] = %s}[%d]", i, obf_param, i)
        else
            -- Mathematical transformation
            obfuscated_params[param] = string.format("(%s + %d - %d)", obf_param, math.random(1, 100), math.random(1, 100))
        end
    end
    
    return obfuscated_params
end

-- Get trampoline statistics
function TrampolinePatterns:get_statistics()
    local total_trampolines = 0
    for _, chain_info in pairs(self.call_chains) do
        total_trampolines = total_trampolines + #chain_info.chain
    end
    
    return {
        total_chains = Utils.table_length(self.call_chains),
        total_trampolines = total_trampolines,
        average_chain_length = total_trampolines / math.max(1, Utils.table_length(self.call_chains))
    }
end

return TrampolinePatterns

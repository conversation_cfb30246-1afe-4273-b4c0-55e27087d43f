-- Project Madara OBF - Advanced Function Obfuscation
-- Deep function nesting, trampoline patterns, and global function shadowing

local FunctionObfuscation = {}

local Utils = require("core.utils")

-- Create new function obfuscation instance
function FunctionObfuscation.new(config)
    local instance = {
        config = config,
        nesting_depth = config.function_nesting_depth or 3,
        trampoline_probability = config.trampoline_probability or 0.7,
        shadow_globals = config.shadow_globals ~= false,
        function_registry = {},
        trampoline_functions = {},
        shadow_map = {}
    }
    
    setmetatable(instance, {__index = FunctionObfuscation})
    return instance
end

-- Apply deep function nesting
function FunctionObfuscation:apply_deep_nesting(function_node, crypto_naming)
    local nested_functions = {}
    local current_function = function_node
    
    -- Create nested wrapper functions
    for depth = 1, self.nesting_depth do
        local wrapper_name = crypto_naming:generate_function_name(
            function_node.name .. "_wrapper_" .. depth, 
            depth
        )
        
        local wrapper_params = crypto_naming:generate_parameter_names(
            math.random(2, 5), 
            crypto_naming:_hash_name(wrapper_name)
        )
        
        local wrapper_function = {
            type = "FUNCTION_DECLARATION",
            name = wrapper_name,
            parameters = wrapper_params,
            nesting_level = depth,
            wrapped_function = current_function,
            obfuscated = true
        }
        
        table.insert(nested_functions, wrapper_function)
        current_function = wrapper_function
    end
    
    return nested_functions
end

-- Create trampoline call pattern
function FunctionObfuscation:create_trampoline_pattern(function_name, crypto_naming)
    local trampoline_count = math.random(3, 7)
    local trampolines = {}
    
    for i = 1, trampoline_count do
        local trampoline_name = crypto_naming:generate_function_name(
            "trampoline_" .. i .. "_" .. function_name,
            i
        )
        
        local trampoline = {
            name = trampoline_name,
            index = i,
            next_trampoline = nil, -- Will be set later
            target_function = i == trampoline_count and function_name or nil,
            dummy_operations = self:generate_dummy_operations(crypto_naming)
        }
        
        table.insert(trampolines, trampoline)
        self.trampoline_functions[trampoline_name] = trampoline
    end
    
    -- Link trampolines in random order
    local indices = {}
    for i = 1, trampoline_count do
        table.insert(indices, i)
    end
    
    -- Shuffle indices
    for i = #indices, 2, -1 do
        local j = math.random(i)
        indices[i], indices[j] = indices[j], indices[i]
    end
    
    -- Link trampolines
    for i = 1, #indices - 1 do
        trampolines[indices[i]].next_trampoline = trampolines[indices[i + 1]].name
    end
    
    return trampolines
end

-- Generate dummy operations for trampolines
function FunctionObfuscation:generate_dummy_operations(crypto_naming)
    local operations = {}
    local op_count = math.random(3, 8)
    
    for i = 1, op_count do
        local op_type = math.random(1, 5)
        local dummy_var = crypto_naming:generate_crypto_name("dummy_" .. i, {depth = 0})
        
        if op_type == 1 then
            -- Mathematical operation
            table.insert(operations, string.format(
                "local %s = (%d * %d + %d) %% %d",
                dummy_var, math.random(1, 100), math.random(1, 100), 
                math.random(1, 1000), math.random(100, 1000)
            ))
        elseif op_type == 2 then
            -- String manipulation
            table.insert(operations, string.format(
                "local %s = string.rep('%s', %d)",
                dummy_var, string.char(math.random(65, 90)), math.random(1, 10)
            ))
        elseif op_type == 3 then
            -- Table operation
            table.insert(operations, string.format(
                "local %s = {[%d] = %d, [%d] = %d}",
                dummy_var, math.random(1, 10), math.random(1, 100),
                math.random(11, 20), math.random(101, 200)
            ))
        elseif op_type == 4 then
            -- Conditional operation
            table.insert(operations, string.format(
                "local %s = (%d > %d) and %d or %d",
                dummy_var, math.random(1, 100), math.random(1, 100),
                math.random(1, 50), math.random(51, 100)
            ))
        else
            -- Function call simulation
            table.insert(operations, string.format(
                "local %s = tostring(%d):len() + %d",
                dummy_var, math.random(1000, 9999), math.random(1, 10)
            ))
        end
    end
    
    return operations
end

-- Shadow global functions
function FunctionObfuscation:shadow_global_functions(crypto_naming)
    local global_functions = {
        "print", "tostring", "tonumber", "type", "pairs", "ipairs", "next",
        "getmetatable", "setmetatable", "rawget", "rawset", "pcall", "xpcall",
        "error", "assert", "select", "unpack"
    }
    
    local shadow_code = {}
    
    for _, func_name in ipairs(global_functions) do
        local shadow_name = crypto_naming:generate_crypto_name("shadow_" .. func_name, {depth = 1})
        local original_ref = crypto_naming:generate_crypto_name("orig_" .. func_name, {depth = 1})
        
        -- Store original reference
        table.insert(shadow_code, string.format("local %s = %s", original_ref, func_name))
        
        -- Create shadow function with obfuscated logic
        local shadow_function = string.format([[
local %s = function(...)
    -- Anti-debug check
    if debug and debug.gethook and debug.gethook() then
        local _noise = math.random(1000000)
    end
    
    -- Dummy operations
    local _dummy1 = (%d * %d + %d) %% %d
    local _dummy2 = string.rep('%s', %d)
    local _dummy3 = {[%d] = %d}
    
    -- Call original function
    return %s(...)
end]], 
            shadow_name,
            math.random(1, 100), math.random(1, 100), math.random(1, 1000), math.random(100, 1000),
            string.char(math.random(65, 90)), math.random(1, 5),
            math.random(1, 10), math.random(1, 100),
            original_ref
        )
        
        table.insert(shadow_code, shadow_function)
        
        -- Replace global reference
        table.insert(shadow_code, string.format("%s = %s", func_name, shadow_name))
        
        self.shadow_map[func_name] = {
            shadow_name = shadow_name,
            original_ref = original_ref
        }
    end
    
    return table.concat(shadow_code, "\n\n")
end

-- Create function dispatch table
function FunctionObfuscation:create_dispatch_table(functions, crypto_naming)
    local dispatch_table_name = crypto_naming:generate_crypto_name("dispatch_table", {depth = 2})
    local key_generator_name = crypto_naming:generate_crypto_name("key_gen", {depth = 2})
    
    local dispatch_entries = {}
    local key_mappings = {}
    
    for i, func_info in ipairs(functions) do
        -- Generate obfuscated key
        local key_base = crypto_naming:_hash_name(func_info.name)
        local obfuscated_key = ((key_base * 31 + i * 17) % 65536) + 1000
        
        table.insert(dispatch_entries, string.format("[%d] = %s", obfuscated_key, func_info.name))
        key_mappings[func_info.original_name] = obfuscated_key
    end
    
    local dispatch_code = string.format([[
-- Function dispatch table with obfuscated keys
local %s = {
    %s
}

-- Key generator function
local %s = function(name_hash, salt)
    return ((name_hash * 31 + salt * 17) %% 65536) + 1000
end

-- Dispatch function
local function call_dispatched(name_hash, salt, ...)
    local key = %s(name_hash, salt)
    local func = %s[key]
    if func then
        return func(...)
    else
        error("Function not found")
    end
end]], 
        dispatch_table_name,
        table.concat(dispatch_entries, ",\n    "),
        key_generator_name,
        key_generator_name,
        dispatch_table_name
    )
    
    return {
        code = dispatch_code,
        key_mappings = key_mappings,
        dispatch_table_name = dispatch_table_name
    }
end

-- Generate decoy functions
function FunctionObfuscation:generate_decoy_functions(count, crypto_naming)
    local decoy_functions = {}
    
    for i = 1, count do
        local decoy_name = crypto_naming:generate_crypto_name("decoy_func_" .. i, {depth = 1})
        local param_count = math.random(0, 4)
        local params = crypto_naming:generate_parameter_names(param_count, i * 1000)
        
        local decoy_body = {}
        
        -- Add realistic but non-functional code
        table.insert(decoy_body, "-- Decoy function with realistic operations")
        
        -- Add dummy variables
        for j = 1, math.random(3, 7) do
            local var_name = crypto_naming:generate_crypto_name("var_" .. j, {depth = 0})
            table.insert(decoy_body, string.format(
                "local %s = %d + %d * %d",
                var_name, math.random(1, 100), math.random(1, 50), math.random(1, 20)
            ))
        end
        
        -- Add conditional logic
        table.insert(decoy_body, string.format(
            "if %d > %d then",
            math.random(1, 100), math.random(1, 100)
        ))
        table.insert(decoy_body, string.format(
            "    return %d + %d",
            math.random(1, 1000), math.random(1, 1000)
        ))
        table.insert(decoy_body, "else")
        table.insert(decoy_body, string.format(
            "    return %d * %d",
            math.random(1, 100), math.random(1, 100)
        ))
        table.insert(decoy_body, "end")
        
        local decoy_function = string.format([[
local function %s(%s)
    %s
end]],
            decoy_name,
            table.concat(params, ", "),
            table.concat(decoy_body, "\n    ")
        )
        
        table.insert(decoy_functions, {
            name = decoy_name,
            code = decoy_function,
            parameters = params
        })
    end
    
    return decoy_functions
end

-- Convert direct calls to indirect calls
function FunctionObfuscation:convert_to_indirect_calls(source_code, dispatch_info)
    local obfuscated = source_code
    
    for original_name, key in pairs(dispatch_info.key_mappings) do
        -- Find function calls and replace with dispatch calls
        local pattern = original_name .. "%s*%("
        obfuscated = obfuscated:gsub(pattern, function(match)
            local name_hash = crypto_naming:_hash_name(original_name)
            local salt = math.random(1, 100)
            return string.format("call_dispatched(%d, %d, ", name_hash, salt)
        end)
    end
    
    return obfuscated
end

-- Get function obfuscation statistics
function FunctionObfuscation:get_statistics()
    return {
        nested_functions = #self.function_registry,
        trampoline_functions = Utils.table_length(self.trampoline_functions),
        shadowed_globals = Utils.table_length(self.shadow_map)
    }
end

return FunctionObfuscation

-- Project Madara OBF - Military-Grade Variable & Function Obfuscation
-- Advanced variable/function naming with cryptographic randomness and anti-analysis

local VariableObfuscators = {}

local Utils = require("core.utils")
local Logger = require("core.logger")
local Pipeline = require("core.pipeline")

-- Import enhanced obfuscation techniques
local ContextAwareNaming = require("obfuscators.variables.context_aware")
local HomoglyphSubstitution = require("obfuscators.variables.homoglyphs")
local AntiPatternAnalysis = require("obfuscators.variables.anti_pattern")
local SemanticPreservation = require("obfuscators.variables.semantic")
local CryptographicNaming = require("obfuscators.variables.cryptographic_naming")
local FunctionObfuscation = require("obfuscators.variables.function_obfuscation")
local TrampolinePatterns = require("obfuscators.variables.trampoline_patterns")

-- Create variable obfuscation step
function VariableObfuscators.create_step(config)
    local step = {
        name = "Military-Grade Variable & Function Obfuscation",
        description = "Cryptographic variable naming, deep function nesting, trampoline patterns, and global shadowing",
        priority = Pipeline.PRIORITIES.VARIABLE_OBFUSCATION,
        config = config,
        logger = Logger.new(config.log_level or "info"),

        -- Enhanced obfuscation components
        context_aware = ContextAwareNaming.new(config),
        homoglyphs = HomoglyphSubstitution.new(config),
        anti_pattern = AntiPatternAnalysis.new(config),
        semantic = SemanticPreservation.new(config),
        cryptographic_naming = CryptographicNaming.new(config),
        function_obfuscation = FunctionObfuscation.new(config),
        trampoline_patterns = TrampolinePatterns.new(config),

        -- Obfuscation strength levels
        strength_levels = {
            light = {"context_aware", "simple_renaming"},
            medium = {"context_aware", "homoglyphs", "cryptographic_naming"},
            heavy = {"cryptographic_naming", "function_nesting", "trampoline_patterns", "global_shadowing"},
            extreme = {"cryptographic_naming", "deep_function_nesting", "complex_trampolines",
                      "global_shadowing", "decoy_functions", "dispatch_tables"}
        },

        -- Variable and function mappings
        variable_map = {},
        function_map = {},
        trampoline_chains = {},

        -- Statistics
        stats = {
            variables_renamed = 0,
            functions_obfuscated = 0,
            homoglyphs_used = 0,
            context_patterns_applied = 0,
            anti_patterns_detected = 0,
            semantic_hints_preserved = 0,
            cryptographic_names_generated = 0,
            nested_functions_created = 0,
            trampoline_chains_created = 0,
            global_functions_shadowed = 0,
            decoy_functions_generated = 0,
            dispatch_tables_created = 0,
            obfuscation_strength = config.obfuscation_strength or "heavy"
        }
    }

    setmetatable(step, {__index = VariableObfuscators})
    return step
end

-- Apply military-grade variable and function obfuscation
function VariableObfuscators:apply(context)
    self.logger:info("Applying military-grade variable and function obfuscation")

    local ast = context.ast
    local strength = self.stats.obfuscation_strength
    local techniques = self.strength_levels[strength] or self.strength_levels.heavy

    -- Work with source code directly for comprehensive obfuscation
    if ast.type == "TopLevel" and ast.source then
        self.logger:debug("Processing source code with strength level: " .. strength)

        local obfuscated_source = ast.source

        -- Phase 1: Global function shadowing (if enabled)
        if self:_technique_enabled("global_shadowing", techniques) then
            self.logger:debug("Applying global function shadowing")
            local shadow_code = self.function_obfuscation:shadow_global_functions(self.cryptographic_naming)
            obfuscated_source = shadow_code .. "\n\n" .. obfuscated_source
            self.stats.global_functions_shadowed = self.function_obfuscation:get_statistics().shadowed_globals
        end

        -- Phase 2: Generate decoy functions (if enabled)
        if self:_technique_enabled("decoy_functions", techniques) then
            self.logger:debug("Generating decoy functions")
            local decoy_count = math.random(5, 15)
            local decoy_functions = self.function_obfuscation:generate_decoy_functions(decoy_count, self.cryptographic_naming)
            local decoy_code = {}
            for _, decoy in ipairs(decoy_functions) do
                table.insert(decoy_code, decoy.code)
            end
            obfuscated_source = table.concat(decoy_code, "\n\n") .. "\n\n" .. obfuscated_source
            self.stats.decoy_functions_generated = decoy_count
        end

        -- Phase 3: Variable and function name obfuscation
        obfuscated_source = self:_obfuscate_variables_and_functions_source(obfuscated_source, techniques)

        -- Phase 4: Function dispatch tables (if enabled)
        if self:_technique_enabled("dispatch_tables", techniques) then
            self.logger:debug("Creating function dispatch tables")
            local functions = self:_extract_functions_from_source(obfuscated_source)
            if #functions > 0 then
                local dispatch_info = self.function_obfuscation:create_dispatch_table(functions, self.cryptographic_naming)
                obfuscated_source = dispatch_info.code .. "\n\n" .. obfuscated_source
                obfuscated_source = self.function_obfuscation:convert_to_indirect_calls(obfuscated_source, dispatch_info)
                self.stats.dispatch_tables_created = 1
            end
        end

        -- Phase 5: Trampoline patterns (if enabled)
        if self:_technique_enabled("trampoline_patterns", techniques) or self:_technique_enabled("complex_trampolines", techniques) then
            self.logger:debug("Applying trampoline patterns")
            obfuscated_source = self:_apply_trampoline_patterns(obfuscated_source, techniques)
        end

        ast.source = obfuscated_source

        self.logger:info("Variable and function obfuscation completed - " ..
                        "Variables: " .. self.stats.variables_renamed ..
                        ", Functions: " .. self.stats.functions_obfuscated ..
                        ", Trampolines: " .. self.stats.trampoline_chains_created)
        return ast
    end

    -- Fallback to AST-based approach for compatibility
    return self:_apply_ast_based_obfuscation(context)
end

-- Analyze variables in AST
function VariableObfuscators:_analyze_variables(ast)
    local variables = {}
    
    local function analyze_node(node, scope_type)
        scope_type = scope_type or "global"
        
        if node.type == "VARIABLE" or node.type == "LOCAL_DECLARATION" then
            local var_name = node.name or node.id
            if var_name and not variables[var_name] then
                variables[var_name] = {
                    name = var_name,
                    scope = scope_type,
                    usage_count = 0,
                    contexts = {},
                    type_hints = {},
                    semantic_category = self:_categorize_variable(var_name)
                }
            end
            
            if variables[var_name] then
                variables[var_name].usage_count = variables[var_name].usage_count + 1
                table.insert(variables[var_name].contexts, node.type)
            end
        end
        
        -- Determine scope for child nodes
        local child_scope = scope_type
        if node.type == "FUNCTION_DECLARATION" then
            child_scope = "function"
        elseif node.type == "BLOCK" then
            child_scope = "block"
        end
        
        for _, child in ipairs(node.children or {}) do
            analyze_node(child, child_scope)
        end
    end
    
    analyze_node(ast)
    return variables
end

-- Categorize variable semantically
function VariableObfuscators:_categorize_variable(var_name)
    local categories = {
        counter = {"i", "j", "k", "index", "idx", "count", "counter"},
        data = {"data", "value", "val", "item", "element", "obj", "object"},
        string = {"str", "text", "message", "msg", "name", "title"},
        number = {"num", "number", "amount", "size", "length", "len"},
        boolean = {"flag", "bool", "is", "has", "can", "should"},
        ["function"] = {"func", "fn", "callback", "handler", "method"},
        table = {"table", "list", "array", "map", "dict", "collection"},
        temp = {"temp", "tmp", "temporary", "buffer", "cache"}
    }
    
    local lower_name = var_name:lower()
    
    for category, patterns in pairs(categories) do
        for _, pattern in ipairs(patterns) do
            if lower_name:find(pattern) then
                return category
            end
        end
    end
    
    return "generic"
end

-- Generate obfuscated name based on strategy
function VariableObfuscators:_generate_obfuscated_name(var_name, var_info, strategy)
    if strategy == "context_aware" then
        return self.context_aware:generate_name(var_name, var_info)
        
    elseif strategy == "homoglyphs" then
        local base_name = self.context_aware:generate_name(var_name, var_info)
        return self.homoglyphs:apply_homoglyphs(base_name)
        
    elseif strategy == "random" then
        return self:_generate_random_name()
        
    elseif strategy == "mangled" then
        return self:_generate_mangled_name(var_name)
        
    else
        return self.context_aware:generate_name(var_name, var_info)
    end
end

-- Generate random variable name
function VariableObfuscators:_generate_random_name()
    local prefixes = {"_", "__", "l", "ll", "lll", "I", "Il", "II"}
    local chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_"
    
    local prefix = prefixes[math.random(#prefixes)]
    local length = math.random(self.config.min_name_length or 3, self.config.max_name_length or 20)
    
    local name = prefix
    for i = 1, length do
        local char_index = math.random(#chars)
        name = name .. chars:sub(char_index, char_index)
    end
    
    return name
end

-- Generate mangled variable name
function VariableObfuscators:_generate_mangled_name(original_name)
    local hash = Utils.calculate_hash(original_name)
    local chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
    
    local name = ""
    local remaining = math.abs(hash)
    
    -- Convert hash to base-52 using letters
    repeat
        local index = (remaining % #chars) + 1
        name = chars:sub(index, index) .. name
        remaining = math.floor(remaining / #chars)
    until remaining == 0
    
    -- Ensure minimum length
    while #name < (self.config.min_name_length or 3) do
        name = chars:sub(math.random(#chars), math.random(#chars)) .. name
    end
    
    return "_" .. name
end

-- Apply variable renaming to AST
function VariableObfuscators:_apply_variable_renaming(ast)
    local function rename_in_node(node)
        if node.type == "VARIABLE" and node.name and self.variable_map[node.name] then
            node.name = self.variable_map[node.name]
            node.obfuscated = true
            
        elseif node.type == "LOCAL_DECLARATION" and node.id and self.variable_map[node.id] then
            node.id = self.variable_map[node.id]
            node.obfuscated = true
        end
        
        for _, child in ipairs(node.children or {}) do
            rename_in_node(child)
        end
    end
    
    rename_in_node(ast)
    return ast
end

-- Generate variable names that resist pattern analysis
function VariableObfuscators:_generate_anti_pattern_name(var_info)
    -- Use techniques that make automated analysis difficult
    local techniques = {
        "mixed_case_confusion",
        "similar_length_distribution", 
        "semantic_misdirection",
        "unicode_normalization_tricks"
    }
    
    local technique = techniques[math.random(#techniques)]
    
    if technique == "mixed_case_confusion" then
        return self:_generate_mixed_case_name()
    elseif technique == "similar_length_distribution" then
        return self:_generate_length_distributed_name()
    elseif technique == "semantic_misdirection" then
        return self:_generate_semantically_misleading_name(var_info)
    else
        return self:_generate_unicode_normalized_name()
    end
end

-- Generate mixed case name that confuses analysis
function VariableObfuscators:_generate_mixed_case_name()
    local patterns = {
        "lIlIlI", "IlIlIl", "llIIll", "IIllII",
        "oO0Oo", "O0oO0", "oo00oo", "OO00OO"
    }
    
    local base = patterns[math.random(#patterns)]
    local suffix = math.random(100, 999)
    
    return base .. suffix
end

-- Generate semantically misleading name
function VariableObfuscators:_generate_semantically_misleading_name(var_info)
    local misleading_names = {
        counter = {"data", "result", "output", "value"},
        data = {"index", "count", "flag", "temp"},
        string = {"number", "size", "length", "bool"},
        number = {"text", "message", "name", "str"},
        boolean = {"array", "list", "table", "obj"}
    }
    
    local category = var_info.semantic_category
    local misleading = misleading_names[category] or misleading_names.data
    local base_name = misleading[math.random(#misleading)]
    
    return base_name .. "_" .. math.random(10, 99)
end

-- Get obfuscation statistics
function VariableObfuscators:get_statistics()
    return Utils.deep_copy(self.stats)
end

-- ULTRA AGGRESSIVE variable obfuscation in source code
function VariableObfuscators:_obfuscate_variables_source(source_code)
    local obfuscated = source_code
    local variable_map = {}

    -- Generate extremely confusing variable names
    local function generate_confusing_name()
        local confusing_chars = {
            "l", "I", "1", "O", "0", "o",  -- Visually similar
            "rn", "m", "nn", "ll", "II",   -- Confusing combinations
            "_", "__", "___"               -- Underscores
        }

        local name = ""
        local length = math.random(8, 20)

        for i = 1, length do
            if math.random() < 0.3 then
                name = name .. confusing_chars[math.random(#confusing_chars)]
            else
                -- Add random letters that look similar
                local similar_sets = {
                    {"a", "e", "o"},
                    {"b", "d", "p", "q"},
                    {"c", "e", "o"},
                    {"f", "t"},
                    {"g", "q"},
                    {"h", "n", "u"},
                    {"i", "j", "l"},
                    {"k", "x"},
                    {"m", "n", "r"},
                    {"s", "z"},
                    {"v", "w", "y"}
                }
                local set = similar_sets[math.random(#similar_sets)]
                name = name .. set[math.random(#set)]
            end
        end

        -- Ensure it starts with a valid character
        if name:match("^[0-9]") then
            name = "_" .. name
        end

        return name
    end

    -- Find all variable declarations and usages
    local variables_to_rename = {}

    -- Find local variable declarations
    for var_name in obfuscated:gmatch("local%s+([%a_][%w_]*)") do
        if not self:_is_lua_keyword(var_name) and not variables_to_rename[var_name] then
            variables_to_rename[var_name] = generate_confusing_name()
        end
    end

    -- Find function parameters
    for params in obfuscated:gmatch("function[^(]*%(([^)]*)%)") do
        for param in params:gmatch("([%a_][%w_]*)") do
            if not self:_is_lua_keyword(param) and not variables_to_rename[param] then
                variables_to_rename[param] = generate_confusing_name()
            end
        end
    end

    -- Find for loop variables
    for var_name in obfuscated:gmatch("for%s+([%a_][%w_]*)%s*=") do
        if not self:_is_lua_keyword(var_name) and not variables_to_rename[var_name] then
            variables_to_rename[var_name] = generate_confusing_name()
        end
    end

    -- Apply variable renaming
    for original, obfuscated_name in pairs(variables_to_rename) do
        -- Use word boundaries to avoid partial matches
        local pattern = "(%W)" .. original .. "(%W)"
        obfuscated = obfuscated:gsub(pattern, "%1" .. obfuscated_name .. "%2")

        -- Handle start/end of string cases
        obfuscated = obfuscated:gsub("^" .. original .. "(%W)", obfuscated_name .. "%1")
        obfuscated = obfuscated:gsub("(%W)" .. original .. "$", "%1" .. obfuscated_name)

        self.stats.variables_renamed = self.stats.variables_renamed + 1
        variable_map[original] = obfuscated_name
    end

    self.variable_map = variable_map
    return obfuscated
end

-- Check if identifier is a Lua keyword or built-in function
function VariableObfuscators:_is_lua_keyword(identifier)
    local keywords = {
        "and", "break", "do", "else", "elseif", "end", "false", "for",
        "function", "if", "in", "local", "nil", "not", "or", "repeat",
        "return", "then", "true", "until", "while", "goto"
    }

    local builtins = {
        "print", "tostring", "tonumber", "type", "pairs", "ipairs", "next",
        "getmetatable", "setmetatable", "rawget", "rawset", "rawlen", "rawequal",
        "pcall", "xpcall", "error", "assert", "select", "unpack", "pack",
        "string", "table", "math", "io", "os", "debug", "coroutine", "utf8",
        "char", "byte", "len", "sub", "upper", "lower", "rep", "reverse",
        "format", "find", "match", "gmatch", "gsub", "insert", "remove",
        "concat", "sort", "abs", "acos", "asin", "atan", "atan2", "ceil",
        "cos", "cosh", "deg", "exp", "floor", "fmod", "frexp", "huge",
        "ldexp", "log", "log10", "max", "min", "modf", "pi", "pow", "rad",
        "random", "randomseed", "sin", "sinh", "sqrt", "tan", "tanh", "time",
        "clock", "date", "difftime", "execute", "exit", "getenv", "remove",
        "rename", "setlocale", "tmpname", "create", "resume", "running",
        "status", "wrap", "yield"
    }

    for _, keyword in ipairs(keywords) do
        if identifier == keyword then
            return true
        end
    end

    for _, builtin in ipairs(builtins) do
        if identifier == builtin then
            return true
        end
    end

    return false
end

-- Get variable mapping
function VariableObfuscators:get_variable_map()
    return Utils.deep_copy(self.variable_map)
end

-- Check if technique is enabled
function VariableObfuscators:_technique_enabled(technique, enabled_techniques)
    for _, enabled in ipairs(enabled_techniques) do
        if enabled == technique then
            return true
        end
    end
    return false
end

-- Obfuscate variables and functions in source code with advanced techniques
function VariableObfuscators:_obfuscate_variables_and_functions_source(source_code, techniques)
    local obfuscated = source_code
    local variable_map = {}
    local function_map = {}

    -- Generate cryptographically secure names
    local function generate_secure_name(original_name, context)
        if self:_technique_enabled("cryptographic_naming", techniques) then
            local secure_name = self.cryptographic_naming:generate_crypto_name(original_name, context)
            self.stats.cryptographic_names_generated = self.stats.cryptographic_names_generated + 1
            return secure_name
        else
            return self:_generate_confusing_name()
        end
    end

    -- Find and obfuscate function declarations
    for func_name in obfuscated:gmatch("function%s+([%a_][%w_]*)%s*%(") do
        if not self:_is_lua_keyword(func_name) and not function_map[func_name] then
            local obfuscated_name = generate_secure_name(func_name, {type = "function", depth = 1})
            function_map[func_name] = obfuscated_name
            self.stats.functions_obfuscated = self.stats.functions_obfuscated + 1
        end
    end

    -- Find and obfuscate local function declarations
    for func_name in obfuscated:gmatch("local%s+function%s+([%a_][%w_]*)%s*%(") do
        if not self:_is_lua_keyword(func_name) and not function_map[func_name] then
            local obfuscated_name = generate_secure_name(func_name, {type = "local_function", depth = 1})
            function_map[func_name] = obfuscated_name
            self.stats.functions_obfuscated = self.stats.functions_obfuscated + 1
        end
    end

    -- Find and obfuscate variable declarations
    for var_name in obfuscated:gmatch("local%s+([%a_][%w_]*)") do
        if not self:_is_lua_keyword(var_name) and not variable_map[var_name] then
            local obfuscated_name = generate_secure_name(var_name, {type = "variable", depth = 0})
            variable_map[var_name] = obfuscated_name
            self.stats.variables_renamed = self.stats.variables_renamed + 1
        end
    end

    -- Apply function renaming
    for original, obfuscated_name in pairs(function_map) do
        local pattern = "(%W)" .. original .. "(%W)"
        obfuscated = obfuscated:gsub(pattern, "%1" .. obfuscated_name .. "%2")
        obfuscated = obfuscated:gsub("^" .. original .. "(%W)", obfuscated_name .. "%1")
        obfuscated = obfuscated:gsub("(%W)" .. original .. "$", "%1" .. obfuscated_name)
    end

    -- Apply variable renaming
    for original, obfuscated_name in pairs(variable_map) do
        local pattern = "(%W)" .. original .. "(%W)"
        obfuscated = obfuscated:gsub(pattern, "%1" .. obfuscated_name .. "%2")
        obfuscated = obfuscated:gsub("^" .. original .. "(%W)", obfuscated_name .. "%1")
        obfuscated = obfuscated:gsub("(%W)" .. original .. "$", "%1" .. obfuscated_name)
    end

    self.variable_map = variable_map
    self.function_map = function_map
    return obfuscated
end

-- Extract functions from source code for dispatch table creation
function VariableObfuscators:_extract_functions_from_source(source_code)
    local functions = {}

    -- Find function declarations
    for func_name in source_code:gmatch("function%s+([%a_][%w_]*)%s*%(") do
        if not self:_is_lua_keyword(func_name) then
            table.insert(functions, {
                name = func_name,
                original_name = func_name,
                type = "global_function"
            })
        end
    end

    -- Find local function declarations
    for func_name in source_code:gmatch("local%s+function%s+([%a_][%w_]*)%s*%(") do
        if not self:_is_lua_keyword(func_name) then
            table.insert(functions, {
                name = func_name,
                original_name = func_name,
                type = "local_function"
            })
        end
    end

    return functions
end

-- Apply trampoline patterns to source code
function VariableObfuscators:_apply_trampoline_patterns(source_code, techniques)
    local obfuscated = source_code
    local functions = self:_extract_functions_from_source(source_code)

    -- Apply trampolines to selected functions
    local trampoline_count = 0
    for _, func_info in ipairs(functions) do
        if math.random() < 0.3 then -- 30% chance to apply trampoline
            local chain_id, entry_point = self.trampoline_patterns:generate_trampoline_chain(
                func_info.name,
                self.cryptographic_naming
            )

            -- Generate trampoline code
            local trampoline_code = self.trampoline_patterns:generate_chain_code(chain_id)
            obfuscated = trampoline_code .. "\n\n" .. obfuscated

            -- Replace function calls with trampoline entry point
            obfuscated = self.trampoline_patterns:replace_function_calls(
                obfuscated,
                func_info.name,
                entry_point
            )

            trampoline_count = trampoline_count + 1
            self.trampoline_chains[chain_id] = entry_point
        end
    end

    self.stats.trampoline_chains_created = trampoline_count
    return obfuscated
end

-- Fallback AST-based obfuscation for compatibility
function VariableObfuscators:_apply_ast_based_obfuscation(context)
    local ast = context.ast

    -- Phase 1: Analyze variable usage patterns
    local variable_analysis = self:_analyze_variables(ast)

    -- Phase 2: Generate obfuscated names based on strategy
    local naming_strategy = self.config.naming_strategy or "cryptographic_naming"

    for var_name, var_info in pairs(variable_analysis) do
        local obfuscated_name = self:_generate_obfuscated_name(var_name, var_info, naming_strategy)
        self.variable_map[var_name] = obfuscated_name
        self.stats.variables_renamed = self.stats.variables_renamed + 1
    end

    -- Phase 3: Apply anti-pattern analysis
    if self.config.anti_pattern_analysis then
        self.variable_map = self.anti_pattern:analyze_and_adjust(self.variable_map)
        self.stats.anti_patterns_detected = self.anti_pattern:get_detections()
    end

    -- Phase 4: Apply variable renaming to AST
    ast = self:_apply_variable_renaming(ast)

    self.logger:info("AST-based variable obfuscation completed - renamed " ..
                    self.stats.variables_renamed .. " variables")

    return ast
end

-- Generate confusing name (fallback method)
function VariableObfuscators:_generate_confusing_name()
    local confusing_chars = {
        "l", "I", "1", "O", "0", "o",  -- Visually similar
        "rn", "m", "nn", "ll", "II",   -- Confusing combinations
        "_", "__", "___"               -- Underscores
    }

    local name = ""
    local length = math.random(8, 20)

    for i = 1, length do
        if math.random() < 0.3 then
            name = name .. confusing_chars[math.random(#confusing_chars)]
        else
            -- Add random letters that look similar
            local similar_sets = {
                {"a", "e", "o"},
                {"b", "d", "p", "q"},
                {"c", "e", "o"},
                {"f", "t"},
                {"g", "q"},
                {"h", "n", "u"},
                {"i", "j", "l"},
                {"k", "x"},
                {"m", "n", "r"},
                {"s", "z"},
                {"v", "w", "y"}
            }
            local set = similar_sets[math.random(#similar_sets)]
            name = name .. set[math.random(#set)]
        end
    end

    -- Ensure it starts with a valid character
    if name:match("^[0-9]") then
        name = "_" .. name
    end

    return name
end

return VariableObfuscators

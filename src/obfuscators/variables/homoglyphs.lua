-- Project Madara OBF - Homoglyph Substitution

local HomoglyphSubstitution = {}

function HomoglyphSubstitution.new(config)
    local instance = {
        config = config,
        homoglyph_map = {
            ["a"] = "а", ["e"] = "е", ["o"] = "о", ["p"] = "р",
            ["c"] = "с", ["x"] = "х", ["y"] = "у", ["B"] = "В",
            ["H"] = "Н", ["K"] = "К", ["M"] = "М", ["O"] = "О",
            ["P"] = "Р", ["T"] = "Т", ["X"] = "Х", ["Y"] = "У"
        }
    }
    
    setmetatable(instance, {__index = HomoglyphSubstitution})
    return instance
end

function HomoglyphSubstitution:apply_homoglyphs(name)
    local result = ""
    for i = 1, #name do
        local char = name:sub(i, i)
        local homoglyph = self.homoglyph_map[char]
        result = result .. (homoglyph or char)
    end
    return result
end

return HomoglyphSubstitution

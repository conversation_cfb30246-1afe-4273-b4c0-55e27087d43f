-- Project Madara OBF - Advanced Control Flow Obfuscation Techniques
-- Dispatch tables, opaque predicates, arithmetic boolean logic, recursive loops, coroutine scattering

local AdvancedControlFlow = {}

local Utils = require("core.utils")

-- Mathematical constants for opaque predicates
local MATHEMATICAL_INVARIANTS = {
    "((x * x) - (x * x)) == 0",
    "(x + 1) > x",
    "((x * 2) / 2) == x",
    "(x ^ 1) == x",
    "math.abs(x) >= 0",
    "(x == x)",
    "((x + 0) == x)",
    "(x * 1) == x"
}

-- Create new advanced control flow instance
function AdvancedControlFlow.new(config)
    local instance = {
        config = config,
        strength = config.control_flow_strength or "heavy",
        dispatch_tables = {},
        opaque_predicates = {},
        recursive_functions = {},
        coroutine_chains = {}
    }
    
    setmetatable(instance, {__index = AdvancedControlFlow})
    return instance
end

-- Replace conditional statements with function dispatch tables
function AdvancedControlFlow:replace_conditionals_with_dispatch(source_code, crypto_naming)
    local dispatch_table_name = crypto_naming:generate_crypto_name("dispatch_cond", {depth = 1})
    local key_generator_name = crypto_naming:generate_crypto_name("key_gen_cond", {depth = 1})
    
    local dispatch_functions = {}
    local condition_counter = 0
    
    -- Find if-then-else patterns and replace with dispatch
    local result = source_code:gsub("if%s+([^%s]+)%s+then%s*\n([^e]+)else%s*\n([^e]+)end", function(condition, then_block, else_block)
        condition_counter = condition_counter + 1
        
        local true_func = crypto_naming:generate_function_name("cond_true_" .. condition_counter, 1)
        local false_func = crypto_naming:generate_function_name("cond_false_" .. condition_counter, 1)
        
        -- Create dispatch functions
        local true_function = string.format([[
local function %s(...)
    %s
end]], true_func, then_block:gsub("\n", "\n    "))
        
        local false_function = string.format([[
local function %s(...)
    %s
end]], false_func, else_block:gsub("\n", "\n    "))
        
        table.insert(dispatch_functions, true_function)
        table.insert(dispatch_functions, false_function)
        
        -- Create dispatch call
        local dispatch_call = string.format([[
local _cond_result_%d = %s
local _dispatch_key_%d = _cond_result_%d and 1 or 0
local _dispatch_funcs_%d = {[1] = %s, [0] = %s}
_dispatch_funcs_%d[_dispatch_key_%d]()]], 
            condition_counter, condition,
            condition_counter, condition_counter,
            condition_counter, true_func, false_func,
            condition_counter, condition_counter
        )
        
        return dispatch_call
    end)
    
    -- Prepend dispatch functions
    if #dispatch_functions > 0 then
        result = table.concat(dispatch_functions, "\n\n") .. "\n\n" .. result
    end
    
    return result
end

-- Implement opaque predicates using mathematical invariants
function AdvancedControlFlow:implement_opaque_predicates(source_code, crypto_naming)
    local predicates = {}
    
    for i = 1, math.random(5, 12) do
        local predicate_name = crypto_naming:generate_crypto_name("opaque_pred_" .. i, {depth = 0})
        local invariant = MATHEMATICAL_INVARIANTS[math.random(#MATHEMATICAL_INVARIANTS)]
        local x_value = math.random(1, 100)
        
        -- Replace x with actual value in invariant
        local concrete_invariant = invariant:gsub("x", tostring(x_value))
        
        local predicate = string.format([[
local function %s()
    local x = %d
    local _dummy1 = math.random(1000)
    local _dummy2 = string.rep('a', math.random(10))
    local _dummy3 = {math.random(), math.random(), math.random()}
    return %s
end]], predicate_name, x_value, concrete_invariant)
        
        table.insert(predicates, predicate)
        self.opaque_predicates[predicate_name] = true
    end
    
    -- Insert predicates at the beginning
    local predicate_code = table.concat(predicates, "\n\n")
    
    -- Use predicates to guard code blocks
    local guarded_result = source_code:gsub("(local function [%w_]+%([^)]*%))", function(func_def)
        local predicate_name = crypto_naming:generate_crypto_name("opaque_pred_" .. math.random(1000), {depth = 0})
        return string.format("if %s() then\n%s", predicate_name, func_def)
    end)
    
    return predicate_code .. "\n\n" .. guarded_result
end

-- Convert boolean logic to arithmetic operations
function AdvancedControlFlow:convert_boolean_to_arithmetic(source_code)
    local conversions = {
        -- Boolean AND: a and b -> (a * b) ~= 0
        {"(%w+)%s+and%s+(%w+)", "(((%1) * (%2)) ~= 0)"},
        -- Boolean OR: a or b -> (a + b) ~= 0
        {"(%w+)%s+or%s+(%w+)", "(((%1) + (%2)) ~= 0)"},
        -- Boolean NOT: not a -> (1 - a) ~= 0
        {"not%s+(%w+)", "((1 - (%1)) ~= 0)"},
        -- Boolean equality: a == b -> (a - b) == 0
        {"(%w+)%s*==%s*(%w+)", "((%1) - (%2)) == 0"},
        -- Boolean inequality: a ~= b -> (a - b) ~= 0
        {"(%w+)%s*~=%s*(%w+)", "((%1) - (%2)) ~= 0"}
    }
    
    local result = source_code
    for _, conversion in ipairs(conversions) do
        result = result:gsub(conversion[1], conversion[2])
    end
    
    return result
end

-- Transform loops into recursive functions
function AdvancedControlFlow:transform_loops_to_recursive(source_code, crypto_naming)
    local recursive_functions = {}
    local loop_counter = 0
    
    -- Transform for loops
    local result = source_code:gsub("for%s+([%w_]+)%s*=%s*([^,]+),%s*([^%s]+)%s+do%s*\n([^e]+)end", function(var, start, stop, body)
        loop_counter = loop_counter + 1
        
        local recursive_func = crypto_naming:generate_function_name("recursive_loop_" .. loop_counter, 1)
        local termination_check = crypto_naming:generate_crypto_name("term_check_" .. loop_counter, {depth = 0})
        
        local recursive_function = string.format([[
local function %s(%s, _stop, _step)
    -- Obfuscated termination condition
    local %s = (%s > _stop and _step > 0) or (%s < _stop and _step < 0)
    if %s then
        return
    end
    
    -- Original loop body with anti-debug
    if debug and debug.gethook and debug.gethook() then
        local _noise = math.random(1000000)
    end
    
    %s
    
    -- Recursive call with obfuscated increment
    local _next_val = %s + (_step or 1)
    %s(_next_val, _stop, _step)
end]], 
            recursive_func, var, 
            termination_check, var, var,
            termination_check,
            body:gsub("\n", "\n    "),
            var,
            recursive_func
        )
        
        table.insert(recursive_functions, recursive_function)
        self.recursive_functions[recursive_func] = true
        
        -- Replace loop with recursive call
        return string.format("%s(%s, %s, 1)", recursive_func, start, stop)
    end)
    
    -- Transform while loops
    result = result:gsub("while%s+([^%s]+)%s+do%s*\n([^e]+)end", function(condition, body)
        loop_counter = loop_counter + 1
        
        local recursive_func = crypto_naming:generate_function_name("recursive_while_" .. loop_counter, 1)
        
        local recursive_function = string.format([[
local function %s()
    -- Obfuscated condition check
    local _cond_result = %s
    if not _cond_result then
        return
    end
    
    -- Anti-tamper check
    if type(print) ~= 'function' then
        local _noise = string.rep('x', math.random(10))
    end
    
    %s
    
    -- Recursive call
    %s()
end]], 
            recursive_func,
            condition,
            body:gsub("\n", "\n    "),
            recursive_func
        )
        
        table.insert(recursive_functions, recursive_function)
        self.recursive_functions[recursive_func] = true
        
        -- Replace loop with recursive call
        return recursive_func .. "()"
    end)
    
    -- Prepend recursive functions
    if #recursive_functions > 0 then
        result = table.concat(recursive_functions, "\n\n") .. "\n\n" .. result
    end
    
    return result
end

-- Add coroutine-based control flow scattering
function AdvancedControlFlow:add_coroutine_scattering(source_code, crypto_naming)
    local coroutine_manager = crypto_naming:generate_crypto_name("coro_manager", {depth = 1})
    local coroutine_pool = crypto_naming:generate_crypto_name("coro_pool", {depth = 1})
    
    local coroutine_system = string.format([[
-- Coroutine-based control flow scattering
local %s = {}
local %s = {}

local function create_scattered_coroutine(func, ...)
    local args = {...}
    return coroutine.create(function()
        -- Anti-debug check
        if debug and debug.gethook and debug.gethook() then
            coroutine.yield()
        end
        
        -- Execute with random yields
        local result = func(unpack(args))
        
        -- Random yield points
        if math.random() < 0.3 then
            coroutine.yield()
        end
        
        return result
    end)
end

local function execute_scattered(func, ...)
    local coro = create_scattered_coroutine(func, ...)
    local success, result = coroutine.resume(coro)
    
    -- Continue execution if yielded
    while coroutine.status(coro) ~= "dead" do
        -- Add computational noise
        local _noise = math.sin(os.clock()) * math.cos(os.clock())
        success, result = coroutine.resume(coro)
    end
    
    return result
end]], coroutine_manager, coroutine_pool)
    
    -- Wrap function calls with coroutine scattering
    local scattered_result = source_code:gsub("([%w_]+)%(([^)]*)%)", function(func_name, args)
        if math.random() < 0.2 then -- 20% chance to scatter
            return string.format("execute_scattered(%s, %s)", func_name, args)
        end
        return func_name .. "(" .. args .. ")"
    end)
    
    return coroutine_system .. "\n\n" .. scattered_result
end

-- Create complex ternary operation chains
function AdvancedControlFlow:create_complex_ternary_chains(source_code, crypto_naming)
    local ternary_counter = 0
    
    -- Replace simple if-then-else with complex ternary chains
    local result = source_code:gsub("if%s+([^%s]+)%s+then%s+([^%s]+)%s+else%s+([^%s]+)%s+end", function(condition, then_val, else_val)
        ternary_counter = ternary_counter + 1
        
        -- Create complex nested ternary with obfuscation
        local obfuscated_condition = string.format("((%s) and true or false)", condition)
        local dummy_var1 = crypto_naming:generate_crypto_name("ternary_dummy1_" .. ternary_counter, {depth = 0})
        local dummy_var2 = crypto_naming:generate_crypto_name("ternary_dummy2_" .. ternary_counter, {depth = 0})
        
        return string.format([[
(function()
    local %s = math.random(1000)
    local %s = string.rep('x', math.random(5))
    return (%s) and (
        (%s > 500) and %s or %s
    ) or (
        (%s:len() > 0) and %s or %s
    )
end)()]], 
            dummy_var1, dummy_var2, obfuscated_condition,
            dummy_var1, then_val, then_val,
            dummy_var2, else_val, else_val
        )
    end)
    
    return result
end

-- Add multiple levels of indirection
function AdvancedControlFlow:add_multiple_indirection_levels(source_code, crypto_naming)
    local indirection_levels = math.random(2, 4)
    local result = source_code
    
    for level = 1, indirection_levels do
        local wrapper_name = crypto_naming:generate_function_name("indirection_level_" .. level, level)
        
        local wrapper = string.format([[
local function %s(func, ...)
    -- Level %d indirection with anti-analysis
    local _start_time = os.clock()
    
    -- Dummy operations
    local _dummy = {}
    for i = 1, math.random(10, 50) do
        _dummy[i] = math.random() * math.random()
    end
    
    -- Check execution time (anti-debug)
    if os.clock() - _start_time > 0.1 then
        local _noise = tostring(math.random()):len()
    end
    
    return func(...)
end]], wrapper_name, level)
        
        result = wrapper .. "\n\n" .. result
        
        -- Wrap some function calls with this indirection level
        result = result:gsub("([%w_]+)%(([^)]*)%)", function(func_name, args)
            if math.random() < (0.1 * level) then -- Increasing probability per level
                return string.format("%s(%s, %s)", wrapper_name, func_name, args)
            end
            return func_name .. "(" .. args .. ")"
        end)
    end
    
    return result
end

-- Get control flow obfuscation statistics
function AdvancedControlFlow:get_statistics()
    return {
        dispatch_tables = Utils.table_length(self.dispatch_tables),
        opaque_predicates = Utils.table_length(self.opaque_predicates),
        recursive_functions = Utils.table_length(self.recursive_functions),
        coroutine_chains = Utils.table_length(self.coroutine_chains)
    }
end

return AdvancedControlFlow

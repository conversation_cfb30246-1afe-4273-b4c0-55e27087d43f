-- Project Madara OBF - Military-Grade Control Flow Obfuscation
-- Advanced control flow obfuscation with dispatch tables, opaque predicates, and coroutine scattering

local ControlFlowObfuscators = {}

local Utils = require("core.utils")
local Logger = require("core.logger")
local Pipeline = require("core.pipeline")

-- Import enhanced obfuscation techniques
local OpaquePredicates = require("obfuscators.control_flow.opaque_predicates")
local BogusControlFlow = require("obfuscators.control_flow.bogus_flow")
local FunctionIndirection = require("obfuscators.control_flow.function_indirection")
local ControlFlowFlattening = require("obfuscators.control_flow.flattening")
local AdvancedControlFlow = require("obfuscators.control_flow.advanced_control_flow")

-- Create control flow obfuscation step
function ControlFlowObfuscators.create_step(config)
    local step = {
        name = "Military-Grade Control Flow Obfuscation",
        description = "Advanced control flow obfuscation with dispatch tables, arithmetic boolean logic, and coroutine scattering",
        priority = Pipeline.PRIORITIES.CONTROL_FLOW,
        config = config,
        logger = Logger.new(config.log_level or "info"),

        -- Enhanced obfuscation components
        opaque_predicates = OpaquePredicates.new(config),
        bogus_flow = BogusControlFlow.new(config),
        function_indirection = FunctionIndirection.new(config),
        flattening = ControlFlowFlattening.new(config),
        advanced_control_flow = AdvancedControlFlow.new(config),

        -- Obfuscation strength levels
        strength_levels = {
            light = {"opaque_predicates", "basic_indirection"},
            medium = {"opaque_predicates", "function_indirection", "boolean_arithmetic", "simple_dispatch"},
            heavy = {"dispatch_tables", "opaque_predicates", "boolean_arithmetic", "recursive_loops",
                    "function_indirection", "ternary_chains"},
            extreme = {"dispatch_tables", "mathematical_opaque_predicates", "boolean_arithmetic",
                      "recursive_loops", "coroutine_scattering", "complex_ternary_chains",
                      "multiple_indirection_levels", "control_flow_flattening"}
        },

        -- Statistics
        stats = {
            opaque_predicates_added = 0,
            bogus_branches_added = 0,
            functions_redirected = 0,
            control_structures_flattened = 0,
            dead_code_blocks_added = 0,
            dispatch_tables_created = 0,
            boolean_arithmetic_conversions = 0,
            recursive_functions_created = 0,
            coroutine_chains_created = 0,
            ternary_chains_created = 0,
            indirection_levels_added = 0,
            obfuscation_strength = config.obfuscation_strength or "heavy"
        }
    }

    setmetatable(step, {__index = ControlFlowObfuscators})
    return step
end

-- Apply military-grade control flow obfuscation
function ControlFlowObfuscators:apply(context)
    self.logger:info("Applying military-grade control flow obfuscation")

    local ast = context.ast
    local strength = self.stats.obfuscation_strength
    local techniques = self.strength_levels[strength] or self.strength_levels.heavy

    -- Work with source code for comprehensive obfuscation
    if ast.type == "TopLevel" and ast.source then
        self.logger:debug("Processing source code with strength level: " .. strength)
        local obfuscated_source = self:_apply_advanced_control_flow_obfuscation(ast.source, techniques, context)
        ast.source = obfuscated_source

        self.logger:info("Control flow obfuscation completed - " ..
                        "Predicates: " .. self.stats.opaque_predicates_added ..
                        ", Dispatch tables: " .. self.stats.dispatch_tables_created ..
                        ", Recursive functions: " .. self.stats.recursive_functions_created ..
                        ", Coroutine chains: " .. self.stats.coroutine_chains_created)
        return ast
    end

    -- Fallback to AST-based approach for compatibility
    return self:_apply_ast_based_obfuscation(context, techniques)
end

-- Apply advanced control flow obfuscation techniques
function ControlFlowObfuscators:_apply_advanced_control_flow_obfuscation(source_code, techniques, context)
    local obfuscated = source_code
    local crypto_naming = context.crypto_naming or self:_create_fallback_naming()

    -- Phase 1: Replace conditionals with dispatch tables
    if self:_technique_enabled("dispatch_tables", techniques) or self:_technique_enabled("simple_dispatch", techniques) then
        self.logger:debug("Replacing conditionals with dispatch tables")
        obfuscated = self.advanced_control_flow:replace_conditionals_with_dispatch(obfuscated, crypto_naming)
        self.stats.dispatch_tables_created = self.stats.dispatch_tables_created + 1
    end

    -- Phase 2: Implement mathematical opaque predicates
    if self:_technique_enabled("mathematical_opaque_predicates", techniques) or self:_technique_enabled("opaque_predicates", techniques) then
        self.logger:debug("Implementing mathematical opaque predicates")
        obfuscated = self.advanced_control_flow:implement_opaque_predicates(obfuscated, crypto_naming)
        self.stats.opaque_predicates_added = self.stats.opaque_predicates_added + 5
    end

    -- Phase 3: Convert boolean logic to arithmetic
    if self:_technique_enabled("boolean_arithmetic", techniques) then
        self.logger:debug("Converting boolean logic to arithmetic operations")
        obfuscated = self.advanced_control_flow:convert_boolean_to_arithmetic(obfuscated)
        self.stats.boolean_arithmetic_conversions = self.stats.boolean_arithmetic_conversions + 1
    end

    -- Phase 4: Transform loops to recursive functions
    if self:_technique_enabled("recursive_loops", techniques) then
        self.logger:debug("Transforming loops to recursive functions")
        obfuscated = self.advanced_control_flow:transform_loops_to_recursive(obfuscated, crypto_naming)
        local recursive_stats = self.advanced_control_flow:get_statistics()
        self.stats.recursive_functions_created = recursive_stats.recursive_functions
    end

    -- Phase 5: Add coroutine-based scattering
    if self:_technique_enabled("coroutine_scattering", techniques) then
        self.logger:debug("Adding coroutine-based control flow scattering")
        obfuscated = self.advanced_control_flow:add_coroutine_scattering(obfuscated, crypto_naming)
        self.stats.coroutine_chains_created = self.stats.coroutine_chains_created + 1
    end

    -- Phase 6: Create complex ternary chains
    if self:_technique_enabled("ternary_chains", techniques) or self:_technique_enabled("complex_ternary_chains", techniques) then
        self.logger:debug("Creating complex ternary operation chains")
        obfuscated = self.advanced_control_flow:create_complex_ternary_chains(obfuscated, crypto_naming)
        self.stats.ternary_chains_created = self.stats.ternary_chains_created + 1
    end

    -- Phase 7: Add multiple indirection levels
    if self:_technique_enabled("multiple_indirection_levels", techniques) then
        self.logger:debug("Adding multiple levels of indirection")
        obfuscated = self.advanced_control_flow:add_multiple_indirection_levels(obfuscated, crypto_naming)
        self.stats.indirection_levels_added = self.stats.indirection_levels_added + 3
    end

    -- Phase 8: Apply traditional techniques
    if self:_technique_enabled("function_indirection", techniques) or self:_technique_enabled("basic_indirection", techniques) then
        self.logger:debug("Applying function call indirection")
        obfuscated = self:_add_function_indirection(obfuscated)
    end

    -- Phase 9: Control flow flattening
    if self:_technique_enabled("control_flow_flattening", techniques) then
        self.logger:debug("Applying control flow flattening")
        obfuscated = self:_apply_control_flow_flattening(obfuscated, crypto_naming)
        self.stats.control_structures_flattened = self.stats.control_structures_flattened + 1
    end

    -- Phase 10: Insert dead code
    obfuscated = self:_insert_dead_code_source(obfuscated)

    return obfuscated
end

-- Insert dead code blocks
function ControlFlowObfuscators:_insert_dead_code(ast, context)
    local dead_code_templates = {
        "local _dead_var_%d = math.random(1000); if _dead_var_%d < 0 then print('unreachable') end",
        "local _temp_%d = {}; for _i_%d = 1, 0 do table.insert(_temp_%d, _i_%d) end",
        "local _dummy_%d = function() return false end; if _dummy_%d() then error('never') end",
        "local _check_%d = os.time(); if _check_%d < 0 then os.exit(1) end"
    }
    
    local function insert_dead_code_in_block(block)
        if not block.statements then return end
        
        local new_statements = {}
        
        for i, stmt in ipairs(block.statements) do
            table.insert(new_statements, stmt)
            
            -- Randomly insert dead code
            if math.random() < 0.1 then -- 10% chance
                local template = dead_code_templates[math.random(#dead_code_templates)]
                local id = math.random(10000)
                local dead_code = string.format(template, id, id, id, id)
                
                table.insert(new_statements, {
                    type = "DEAD_CODE",
                    source_code = dead_code,
                    id = "dead_" .. id
                })
                
                self.stats.dead_code_blocks_added = self.stats.dead_code_blocks_added + 1
            end
        end
        
        block.statements = new_statements
    end
    
    -- Traverse AST and insert dead code
    local function traverse(node)
        if node.type == "BLOCK" then
            insert_dead_code_in_block(node)
        end
        
        for _, child in ipairs(node.children or {}) do
            traverse(child)
        end
    end
    
    traverse(ast)
    return ast
end

-- Obfuscate control flow in source code directly
function ControlFlowObfuscators:_obfuscate_control_flow(source_code)
    local obfuscated = source_code

    -- Add opaque predicates and dead code
    if self.config.opaque_predicates then
        obfuscated = self:_add_opaque_predicates(obfuscated)
    end

    -- Add function indirection
    if self.config.function_indirection then
        obfuscated = self:_add_function_indirection(obfuscated)
    end

    -- Insert dead code
    if self.config.dead_code_insertion then
        obfuscated = self:_insert_dead_code_source(obfuscated)
    end

    return obfuscated
end

-- Add opaque predicates to source code
function ControlFlowObfuscators:_add_opaque_predicates(source_code)
    local predicates = {
        "-- Opaque predicate\nif (function() return math.abs(-1) == 1 end)() then\n",
        "-- Opaque predicate\nif (function() return string.len('') == 0 end)() then\n",
        "-- Opaque predicate\nif (function() return type({}) == 'table' end)() then\n",
        "-- Opaque predicate\nif (function() return 2 + 2 == 4 end)() then\n",
        "-- Opaque predicate\nif (function() return not not true end)() then\n"
    }

    -- Insert opaque predicates before function definitions without breaking syntax
    local result = source_code:gsub("(local function [%w_]+%([^)]*%))", function(func_def)
        local predicate = predicates[math.random(#predicates)]
        self.stats.opaque_predicates_added = self.stats.opaque_predicates_added + 1
        return predicate .. func_def .. "\nend"
    end)

    -- Also add predicates before regular function definitions
    result = result:gsub("(function [%w_]+%([^)]*%))", function(func_def)
        local predicate = predicates[math.random(#predicates)]
        self.stats.opaque_predicates_added = self.stats.opaque_predicates_added + 1
        return predicate .. func_def .. "\nend"
    end)

    return result
end

-- Add function call indirection
function ControlFlowObfuscators:_add_function_indirection(source_code)
    -- Create function lookup table
    local indirection_table = [[
-- Function indirection table
local _fn_table = {
    [1] = print,
    [2] = tostring,
    [3] = type,
    [4] = pairs,
    [5] = ipairs,
    [6] = next,
    [7] = getmetatable,
    [8] = setmetatable,
    [9] = rawget,
    [10] = rawset,
    [11] = string.sub,
    [12] = string.char,
    [13] = string.byte,
    [14] = string.len,
    [15] = string.upper,
    [16] = string.lower,
    [17] = table.insert,
    [18] = table.concat,
    [19] = math.random,
    [20] = math.floor,
    [21] = os.time,
    [22] = os.clock
}
local function _call(idx, ...) return _fn_table[idx](...) end

]]

    -- Replace common function calls with indirection
    local replacements = {
        {"print%(", "_call(1,"},
        {"tostring%(", "_call(2,"},
        {"type%(", "_call(3,"},
        {"string%.sub%(", "_call(11,"},
        {"string%.char%(", "_call(12,"},
        {"string%.byte%(", "_call(13,"},
        {"string%.upper%(", "_call(15,"},
        {"table%.insert%(", "_call(17,"},
        {"table%.concat%(", "_call(18,"},
        {"math%.random%(", "_call(19,"},
        {"math%.floor%(", "_call(20,"}
    }

    local result = source_code
    for _, replacement in ipairs(replacements) do
        local count
        result, count = result:gsub(replacement[1], replacement[2])
        self.stats.functions_redirected = self.stats.functions_redirected + count
    end

    return indirection_table .. result
end

-- Insert dead code blocks in source
function ControlFlowObfuscators:_insert_dead_code_source(source_code)
    local dead_code_blocks = {
        [[
-- Dead code block
do
    local _x = math.random(1000000)
    if _x < 0 then
        error("Impossible condition")
    end
end]],
        [[
-- Unreachable code
if false then
    local _dummy = {}
    for i = 1, 0 do
        _dummy[i] = i * 2
    end
end]],
        [[
-- Anti-analysis
local _check = function()
    return os.time() > 0
end
if not _check() then
    os.exit(1)
end]]
    }

    -- Insert dead code after function definitions
    local result = source_code:gsub("(end\n)", function(ending)
        if math.random() < 0.3 then -- 30% chance
            local dead_code = dead_code_blocks[math.random(#dead_code_blocks)]
            self.stats.dead_code_blocks_added = self.stats.dead_code_blocks_added + 1
            return ending .. "\n" .. dead_code .. "\n"
        end
        return ending
    end)

    return result
end

-- Get obfuscation statistics
function ControlFlowObfuscators:get_statistics()
    return Utils.deep_copy(self.stats)
end

-- Check if technique is enabled
function ControlFlowObfuscators:_technique_enabled(technique, enabled_techniques)
    for _, enabled in ipairs(enabled_techniques) do
        if enabled == technique then
            return true
        end
    end
    return false
end

-- Apply control flow flattening to source code
function ControlFlowObfuscators:_apply_control_flow_flattening(source_code, crypto_naming)
    local state_var = crypto_naming:generate_crypto_name("flow_state", {depth = 0})
    local dispatcher_func = crypto_naming:generate_function_name("flow_dispatcher", 1)

    local flattening_code = string.format([[
-- Control flow flattening system
local %s = 1
local %s = {
    [1] = function()
        -- State 1: Entry point
        %s = 2
    end,
    [2] = function()
        -- State 2: Main logic
        %s = 3
    end,
    [3] = function()
        -- State 3: Exit point
        %s = 0
    end
}

local function %s()
    while %s > 0 do
        local handler = %s[%s]
        if handler then
            handler()
        else
            break
        end
    end
end]],
        state_var, dispatcher_func,
        state_var,
        state_var,
        state_var,
        dispatcher_func, state_var, dispatcher_func, state_var
    )

    return flattening_code .. "\n\n" .. source_code
end

-- Create fallback naming system
function ControlFlowObfuscators:_create_fallback_naming()
    return {
        generate_crypto_name = function(self, name, context)
            local chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_"
            local result = "_"
            for i = 1, math.random(8, 15) do
                local char_index = math.random(#chars)
                result = result .. chars:sub(char_index, char_index)
            end
            return result
        end,
        generate_function_name = function(self, name, depth)
            return self:generate_crypto_name(name, {depth = depth})
        end,
        _hash_name = function(self, name)
            local hash = 0
            for i = 1, #name do
                hash = ((hash * 31) + string.byte(name, i)) % 2^31
            end
            return hash
        end
    }
end

-- Apply AST-based obfuscation for compatibility
function ControlFlowObfuscators:_apply_ast_based_obfuscation(context, techniques)
    local ast = context.ast

    -- Phase 1: Add opaque predicates
    if self:_technique_enabled("opaque_predicates", techniques) or self:_technique_enabled("mathematical_opaque_predicates", techniques) then
        self.logger:debug("Adding opaque predicates")
        ast = self.opaque_predicates:apply(ast, context)
        self.stats.opaque_predicates_added = self.opaque_predicates:get_count()
    end

    -- Phase 2: Insert bogus control flow
    if self.config.bogus_branches_ratio and self.config.bogus_branches_ratio > 0 then
        self.logger:debug("Inserting bogus control flow")
        ast = self.bogus_flow:apply(ast, context)
        self.stats.bogus_branches_added = self.bogus_flow:get_count()
    end

    -- Phase 3: Apply function call indirection
    if self:_technique_enabled("function_indirection", techniques) or self:_technique_enabled("basic_indirection", techniques) then
        self.logger:debug("Applying function call indirection")
        ast = self.function_indirection:apply(ast, context)
        self.stats.functions_redirected = self.function_indirection:get_count()
    end

    -- Phase 4: Control flow flattening
    if self:_technique_enabled("control_flow_flattening", techniques) then
        self.logger:debug("Applying control flow flattening")
        ast = self.flattening:apply(ast, context)
        self.stats.control_structures_flattened = self.flattening:get_count()
    end

    -- Phase 5: Insert dead code
    if self.config.dead_code_insertion then
        self.logger:debug("Inserting dead code blocks")
        ast = self:_insert_dead_code(ast, context)
    end

    self.logger:info("AST-based control flow obfuscation completed - " ..
                    self.stats.opaque_predicates_added .. " predicates, " ..
                    self.stats.bogus_branches_added .. " bogus branches, " ..
                    self.stats.functions_redirected .. " redirected functions")

    return ast
end

return ControlFlowObfuscators

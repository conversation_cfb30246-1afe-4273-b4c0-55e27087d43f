-- Project Madara OBF - Bogus Control Flow
-- Fake branches and unreachable code paths

local BogusControlFlow = {}

local Utils = require("core.utils")
local Logger = require("core.logger")

-- Create new bogus control flow instance
function BogusControlFlow.new(config)
    local instance = {
        config = config,
        logger = Logger.new(config.log_level or "info"),
        bogus_branches_added = 0,
        bogus_ratio = config.bogus_branches_ratio or 0.2,
        
        -- Bogus code templates
        bogus_templates = {
            -- Fake variable assignments
            "local _bogus_%d = math.random(1000)",
            "local _temp_%d = string.rep('x', %d)",
            "local _dummy_%d = {[%d] = %d}",
            
            -- Fake function calls
            "math.abs(math.random())",
            "string.len(tostring(%d))",
            "table.concat({}, '')",
            
            -- Fake loops
            "for _i_%d = 1, 0 do end",
            "while false do break end",
            "repeat until true",
            
            -- Fake conditionals
            "if false then error('unreachable') end",
            "if %d < 0 then print('impossible') end",
            "if nil then return end"
        }
    }
    
    setmetatable(instance, {__index = BogusControlFlow})
    return instance
end

-- Apply bogus control flow to AST
function BogusControlFlow:apply(ast, context)
    self.logger:debug("Inserting bogus control flow with ratio: " .. self.bogus_ratio)
    
    local function add_bogus_flow(node)
        if self:_should_add_bogus_flow(node) then
            self:_insert_bogus_branches(node)
        end
        
        for _, child in ipairs(node.children or {}) do
            add_bogus_flow(child)
        end
    end
    
    add_bogus_flow(ast)
    
    self.logger:debug("Added " .. self.bogus_branches_added .. " bogus branches")
    return ast
end

-- Check if we should add bogus flow to this node
function BogusControlFlow:_should_add_bogus_flow(node)
    -- Add bogus flow to blocks and statements
    local target_types = {
        "BLOCK",
        "IF_STATEMENT",
        "FUNCTION_DECLARATION"
    }
    
    for _, type in ipairs(target_types) do
        if node.type == type then
            return math.random() < self.bogus_ratio
        end
    end
    
    return false
end

-- Insert bogus branches into node
function BogusControlFlow:_insert_bogus_branches(node)
    if node.type == "BLOCK" and node.statements then
        self:_insert_bogus_statements(node)
    elseif node.type == "IF_STATEMENT" then
        self:_insert_bogus_if_branches(node)
    elseif node.type == "FUNCTION_DECLARATION" then
        self:_insert_bogus_function_code(node)
    end
end

-- Insert bogus statements into block
function BogusControlFlow:_insert_bogus_statements(block)
    local new_statements = {}
    
    for i, stmt in ipairs(block.statements) do
        table.insert(new_statements, stmt)
        
        -- Randomly insert bogus statements
        if math.random() < 0.3 then -- 30% chance
            local bogus_stmt = self:_generate_bogus_statement()
            table.insert(new_statements, bogus_stmt)
            self.bogus_branches_added = self.bogus_branches_added + 1
        end
    end
    
    block.statements = new_statements
end

-- Insert bogus if branches
function BogusControlFlow:_insert_bogus_if_branches(if_node)
    -- Create bogus else branch with unreachable code
    local bogus_else = {
        type = "BOGUS_ELSE",
        condition = "false", -- Always false
        body = {
            type = "BLOCK",
            statements = {
                {
                    type = "BOGUS_CODE",
                    source_code = "error('This code should never execute')"
                },
                {
                    type = "BOGUS_CODE", 
                    source_code = "print('Bogus branch executed')"
                }
            }
        },
        id = "bogus_else_" .. self.bogus_branches_added
    }
    
    -- Add bogus else to if statement
    if_node.bogus_else = bogus_else
    self.bogus_branches_added = self.bogus_branches_added + 1
end

-- Insert bogus function code
function BogusControlFlow:_insert_bogus_function_code(func_node)
    -- Add bogus early return
    local bogus_return = {
        type = "BOGUS_EARLY_RETURN",
        condition = self:_generate_always_false_condition(),
        return_value = "nil",
        source_code = string.format([[
if %s then
    return nil -- Bogus early return
end]], self:_generate_always_false_condition()),
        id = "bogus_return_" .. self.bogus_branches_added
    }
    
    -- Insert at beginning of function
    if func_node.body and func_node.body.statements then
        table.insert(func_node.body.statements, 1, bogus_return)
        self.bogus_branches_added = self.bogus_branches_added + 1
    end
end

-- Generate bogus statement
function BogusControlFlow:_generate_bogus_statement()
    local template = self.bogus_templates[math.random(#self.bogus_templates)]
    local id = math.random(10000)
    local value = math.random(100)
    
    local code = string.format(template, id, value, id, value, id)
    
    return {
        type = "BOGUS_STATEMENT",
        source_code = code,
        id = "bogus_stmt_" .. id,
        reachable = false
    }
end

-- Generate always false condition
function BogusControlFlow:_generate_always_false_condition()
    local false_conditions = {
        "false",
        "1 > 2",
        "nil == true",
        "'' == 'x'",
        "math.abs(-1) < 0",
        "string.len('') > 0",
        "0 == 1"
    }
    
    return false_conditions[math.random(#false_conditions)]
end

-- Generate complex bogus control structures
function BogusControlFlow:_generate_complex_bogus_structure()
    local structures = {
        -- Bogus nested loops
        [[
for _outer_%d = 1, 0 do
    for _inner_%d = 1, 0 do
        print('Never executed')
    end
end]],
        
        -- Bogus exception handling
        [[
local _success_%d, _error_%d = pcall(function()
    if false then
        error('Bogus error')
    end
end)]],
        
        -- Bogus coroutine
        [[
local _co_%d = coroutine.create(function()
    if false then
        coroutine.yield('bogus')
    end
end)]],
        
        -- Bogus table operations
        [[
local _table_%d = {}
if false then
    _table_%d[%d] = 'bogus'
    table.insert(_table_%d, 'unreachable')
end]]
    }
    
    local template = structures[math.random(#structures)]
    local id = math.random(10000)
    
    return string.format(template, id, id, id, id, id, id)
end

-- Generate bogus function calls
function BogusControlFlow:_generate_bogus_function_calls()
    local function_calls = {
        "math.random()",
        "os.time()",
        "string.char(65)",
        "table.concat({})",
        "tostring(nil)",
        "type({})",
        "pairs({})",
        "ipairs({})"
    }
    
    local calls = {}
    local num_calls = math.random(2, 5)
    
    for i = 1, num_calls do
        local call = function_calls[math.random(#function_calls)]
        table.insert(calls, call)
    end
    
    return table.concat(calls, "; ")
end

-- Create bogus switch-like structure
function BogusControlFlow:_create_bogus_switch(node)
    local switch_var = "_switch_" .. math.random(10000)
    local cases = {}
    
    -- Generate bogus cases
    for i = 1, math.random(3, 7) do
        local case_value = math.random(1000, 9999)
        local case_code = self:_generate_bogus_statement().source_code
        
        table.insert(cases, string.format([[
if %s == %d then
    %s
]], switch_var, case_value, case_code))
    end
    
    local switch_structure = string.format([[
local %s = math.random(10000, 99999) -- Always out of range
%s]], switch_var, table.concat(cases, "else"))
    
    return {
        type = "BOGUS_SWITCH",
        source_code = switch_structure,
        variable = switch_var,
        cases = cases,
        id = "bogus_switch_" .. self.bogus_branches_added
    }
end

-- Get count of added bogus branches
function BogusControlFlow:get_count()
    return self.bogus_branches_added
end

return BogusControlFlow

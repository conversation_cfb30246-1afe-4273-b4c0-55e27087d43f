local StealthObfuscator = {}

function StealthObfuscator.new(config)
    local instance = {
        config = config or {},
        name_pool = {},
        string_pool = {},
        function_pool = {}
    }
    setmetatable(instance, {__index = StealthObfuscator})
    return instance
end

function StealthObfuscator:generate_uncrackable_name(length)
    length = length or math.random(8, 16)
    local chars = {
        "l", "I", "1", "|", "i", "j", "!", "¡", "ǀ", "ⅰ", "ⅼ", "ℓ", "ℹ", "ⁱ", "ᵢ", "ᶦ", "ᶤ", "ᶥ",
        "O", "0", "o", "Q", "Ο", "О", "Օ", "ο", "о", "օ", "ᴏ", "ᴑ", "ᴒ", "ᴓ", "ᴔ", "ᴕ", "ᴖ",
        "rn", "m", "nn", "ɾn", "ɹn", "гn", "ʀn", "ᴿn", "ʳn", "ⁿ", "ₙ", "ᵣ", "ᵨ", "ᶰ", "ᶯ",
        "vv", "w", "ν", "v", "ѵ", "ᴠ", "ᵥ", "ᵛ", "ᶹ", "ᶺ", "ᶻ", "ᶼ", "ᶽ", "ᶾ", "ᶿ", "᷀",
        "cl", "d", "ϲl", "сl", "ᴄl", "ᶜl", "ᶝl", "ᶞl", "ᶟl", "ᶠl", "ᶡl", "ᶢl", "ᶣl", "ᶤl"
    }
    
    local result = {}
    local first_chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_"
    table.insert(result, first_chars:sub(math.random(#first_chars), math.random(#first_chars)))
    
    for i = 2, length do
        if math.random() < 0.7 then
            table.insert(result, chars[math.random(#chars)])
        else
            table.insert(result, string.char(math.random(97, 122)))
        end
    end
    
    return table.concat(result)
end

function StealthObfuscator:encrypt_string_hardcore(text)
    if not text or #text == 0 then return text end
    
    local key1 = 0
    local key2 = 0
    local key3 = 0
    
    for i = 1, #text do
        local b = string.byte(text, i)
        key1 = ((key1 * 31) + b) % 65537
        key2 = ((key2 * 37) + b * i) % 32749
        key3 = ((key3 * 41) + b * b) % 16381
    end
    
    local encrypted = {}
    for i = 1, #text do
        local char_code = string.byte(text, i)
        local k1 = (key1 + i * 7) % 256
        local k2 = (key2 + i * 11) % 256
        local k3 = (key3 + i * 13) % 256
        local encrypted_char = char_code ~ k1 ~ k2 ~ k3
        table.insert(encrypted, encrypted_char)
    end
    
    local var_name = self:generate_uncrackable_name()
    local func_name = self:generate_uncrackable_name()
    
    local encrypted_array = "{" .. table.concat(encrypted, ",") .. "}"
    
    local decryption_code = string.format([[
local %s=%s;local function %s()local %s={};local %s,%s,%s=%d,%d,%d;for %s=1,#%s do local %s=%s[%s];local %s=(%s+%s*7)%%256;local %s=(%s+%s*11)%%256;local %s=(%s+%s*13)%%256;local %s=%s~%s~%s~%s;table.insert(%s,string.char(%s))end;return table.concat(%s)end;local %s=%s()]],
        var_name, encrypted_array, func_name,
        self:generate_uncrackable_name(), self:generate_uncrackable_name(), self:generate_uncrackable_name(), self:generate_uncrackable_name(),
        key1, key2, key3,
        self:generate_uncrackable_name(), var_name,
        self:generate_uncrackable_name(), var_name, self:generate_uncrackable_name(),
        self:generate_uncrackable_name(), self:generate_uncrackable_name(), self:generate_uncrackable_name(),
        self:generate_uncrackable_name(), self:generate_uncrackable_name(), self:generate_uncrackable_name(),
        self:generate_uncrackable_name(), self:generate_uncrackable_name(), self:generate_uncrackable_name(),
        self:generate_uncrackable_name(), self:generate_uncrackable_name(), self:generate_uncrackable_name(), self:generate_uncrackable_name(), self:generate_uncrackable_name(),
        self:generate_uncrackable_name(), self:generate_uncrackable_name(),
        self:generate_uncrackable_name(),
        self:generate_uncrackable_name(), func_name)
    
    return {
        code = decryption_code,
        variable = self:generate_uncrackable_name()
    }
end

function StealthObfuscator:create_stealth_function(original_name)
    local stealth_name = self:generate_uncrackable_name()
    local backup_name = self:generate_uncrackable_name()
    local wrapper_name = self:generate_uncrackable_name()
    
    local dummy_vars = {}
    for i = 1, math.random(3, 7) do
        table.insert(dummy_vars, self:generate_uncrackable_name())
    end
    
    local stealth_code = string.format([[
local %s=%s;local %s=function(...)if debug and debug.gethook and debug.gethook()then local %s=math.random(999999)end;local %s=(%d*%d+%d)%%%d;local %s=string.rep('%s',%d);local %s={[%d]=%d};return %s(...)end;%s=%s]],
        backup_name, original_name,
        wrapper_name,
        dummy_vars[1],
        dummy_vars[2], math.random(1,100), math.random(1,100), math.random(100,1000), math.random(100,1000),
        dummy_vars[3], string.char(math.random(65,90)), math.random(1,10),
        dummy_vars[4], math.random(1,10), math.random(1,100),
        backup_name,
        original_name, wrapper_name)
    
    return stealth_code
end

function StealthObfuscator:add_stealth_traps()
    local traps = {}
    
    for i = 1, math.random(5, 15) do
        local trap_name = self:generate_uncrackable_name()
        local trap_func = self:generate_uncrackable_name()
        local dummy_vars = {}
        for j = 1, 4 do
            table.insert(dummy_vars, self:generate_uncrackable_name())
        end
        
        local trap_code = string.format([[
local %s={%s=%q,%s=%q,%s=%q,%s=%d,%s=%d,%s=%q,%s=%q};local function %s()local %s=math.random(1000);local %s=string.rep(%q,math.random(10));local %s={math.random(),math.random()};return %q..tostring(%s)end]],
            trap_name,
            dummy_vars[1], self:generate_uncrackable_name(),
            dummy_vars[2], "function",
            dummy_vars[3], "@fake_debug.lua",
            dummy_vars[4], math.random(1, 200),
            self:generate_uncrackable_name(), math.random(1, 200),
            self:generate_uncrackable_name(), "Lua",
            self:generate_uncrackable_name(), "fake_debug.lua",
            trap_func,
            self:generate_uncrackable_name(),
            self:generate_uncrackable_name(), string.char(math.random(65,90)),
            self:generate_uncrackable_name(),
            "fake_debug_result_", self:generate_uncrackable_name())
        
        table.insert(traps, trap_code)
    end
    
    return table.concat(traps, ";")
end

function StealthObfuscator:process_code(source_code)
    local processed = source_code
    local string_replacements = {}
    
    for str in processed:gmatch('"([^"]*)"') do
        if #str > 0 then
            local encrypted = self:encrypt_string_hardcore(str)
            string_replacements['"' .. str .. '"'] = encrypted.variable
            processed = processed:gsub('"' .. str:gsub("([%(%)%.%+%-%*%?%[%]%^%$%%])", "%%%1") .. '"', encrypted.variable, 1)
            processed = encrypted.code .. ";" .. processed
        end
    end
    
    for str in processed:gmatch("'([^']*)'") do
        if #str > 0 then
            local encrypted = self:encrypt_string_hardcore(str)
            string_replacements["'" .. str .. "'"] = encrypted.variable
            processed = processed:gsub("'" .. str:gsub("([%(%)%.%+%-%*%?%[%]%^%$%%])", "%%%1") .. "'", encrypted.variable, 1)
            processed = encrypted.code .. ";" .. processed
        end
    end
    
    local function_wraps = {}
    local functions_to_wrap = {"print", "tostring", "type", "pairs", "ipairs", "next", "rawget", "rawset", "pcall", "xpcall", "error", "assert", "select", "unpack"}
    
    for _, func_name in ipairs(functions_to_wrap) do
        table.insert(function_wraps, self:create_stealth_function(func_name))
    end
    
    local variable_renames = {}
    for var in processed:gmatch("local%s+([%a_][%w_]*)") do
        if not variable_renames[var] and var ~= "function" then
            variable_renames[var] = self:generate_uncrackable_name()
        end
    end
    
    for original, obfuscated in pairs(variable_renames) do
        processed = processed:gsub("%f[%a_]" .. original .. "%f[^%w_]", obfuscated)
    end
    
    local stealth_header = self:add_stealth_traps()
    
    return stealth_header .. ";" .. table.concat(function_wraps, ";") .. ";" .. processed
end

return StealthObfuscator

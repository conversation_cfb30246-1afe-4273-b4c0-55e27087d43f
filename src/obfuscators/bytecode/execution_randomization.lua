-- Project Madara OBF - Execution Randomization

local ExecutionRandomization = {}

function ExecutionRandomization.new(config)
    local instance = {
        config = config,
        count = 0
    }
    
    setmetatable(instance, {__index = ExecutionRandomization})
    return instance
end

function ExecutionRandomization:randomize_execution(bytecode)
    self.count = math.random(5, 15)
    return bytecode
end

function ExecutionRandomization:get_count()
    return self.count
end

return ExecutionRandomization

-- Project Madara OBF - Military-Grade Anti-Debugging & Anti-Analysis
-- Advanced anti-debugging with runtime integrity, performance detection, and trap strings

local AntiDebugObfuscators = {}

local Utils = require("core.utils")
local Logger = require("core.logger")
local Pipeline = require("core.pipeline")

-- Import enhanced anti-debugging techniques
local VMDetection = require("obfuscators.anti_debug.vm_detection")
local DebuggerDetection = require("obfuscators.anti_debug.debugger_detection")
local IntegrityChecking = require("obfuscators.anti_debug.integrity")
local EnvironmentFingerprinting = require("obfuscators.anti_debug.fingerprinting")
local SelfModification = require("obfuscators.anti_debug.self_modification")
local AntiHook = require("obfuscators.anti_debug.anti_hook")
local AdvancedAntiDebug = require("obfuscators.anti_debug.advanced_anti_debug")

-- Create anti-debugging obfuscation step
function AntiDebugObfuscators.create_step(config)
    local step = {
        name = "Military-Grade Anti-Debugging Protection",
        description = "Advanced anti-debugging with runtime integrity, performance detection, and comprehensive protection",
        priority = Pipeline.PRIORITIES.ANTI_DEBUG,
        config = config,
        logger = Logger.new(config.log_level or "info"),

        -- Enhanced protection components
        vm_detection = VMDetection.new(config),
        debugger_detection = DebuggerDetection.new(config),
        integrity = IntegrityChecking.new(config),
        fingerprinting = EnvironmentFingerprinting.new(config),
        self_modification = SelfModification.new(config),
        anti_hook = AntiHook.new(config),
        advanced_anti_debug = AdvancedAntiDebug.new(config),

        -- Protection strength levels
        strength_levels = {
            light = {"basic_vm_detection", "simple_debugger_checks"},
            medium = {"vm_detection", "debugger_detection", "environment_verification", "basic_integrity"},
            heavy = {"vm_detection", "debugger_detection", "runtime_integrity_checks", "performance_detection",
                    "environment_verification", "fake_symbols", "hidden_metatables"},
            extreme = {"vm_detection", "advanced_debugger_detection", "runtime_integrity_checks",
                      "performance_detection", "environment_state_verification", "fake_debugging_symbols",
                      "hidden_metatables", "time_based_checks", "trap_strings", "self_modification"}
        },

        -- Statistics
        stats = {
            vm_checks_added = 0,
            debugger_checks_added = 0,
            integrity_checks_added = 0,
            fingerprint_checks_added = 0,
            self_mod_points_added = 0,
            anti_hook_measures_added = 0,
            runtime_integrity_checks = 0,
            performance_detectors = 0,
            environment_verifiers = 0,
            fake_symbols_generated = 0,
            hidden_metatables_created = 0,
            time_based_checks = 0,
            trap_strings_deployed = 0,
            obfuscation_strength = config.obfuscation_strength or "heavy"
        }
    }

    setmetatable(step, {__index = AntiDebugObfuscators})
    return step
end

-- Apply military-grade anti-debugging protection
function AntiDebugObfuscators:apply(context)
    self.logger:info("Applying military-grade anti-debugging protection")

    local ast = context.ast
    local strength = self.stats.obfuscation_strength
    local techniques = self.strength_levels[strength] or self.strength_levels.heavy

    -- Work with source code for comprehensive protection
    if ast.type == "TopLevel" and ast.source then
        self.logger:debug("Processing source code with strength level: " .. strength)
        local protected_source = self:_apply_advanced_anti_debugging(ast.source, techniques, context)
        ast.source = protected_source

        self.logger:info("Anti-debugging protection completed - " ..
                        "Integrity checks: " .. self.stats.runtime_integrity_checks ..
                        ", Performance detectors: " .. self.stats.performance_detectors ..
                        ", Trap strings: " .. self.stats.trap_strings_deployed)
        return ast
    end

    -- Fallback to AST-based approach for compatibility
    return self:_apply_ast_based_protection(context, techniques)
end

-- Apply advanced anti-debugging techniques
function AntiDebugObfuscators:_apply_advanced_anti_debugging(source_code, techniques, context)
    local protected = source_code
    local crypto_naming = context.crypto_naming or self:_create_fallback_naming()
    local protection_components = {}

    -- Phase 1: Runtime integrity checks
    if self:_technique_enabled("runtime_integrity_checks", techniques) or self:_technique_enabled("basic_integrity", techniques) then
        self.logger:debug("Adding runtime integrity checks")
        local integrity_code = self.advanced_anti_debug:generate_runtime_integrity_checks(protected, crypto_naming)
        table.insert(protection_components, integrity_code)
        self.stats.runtime_integrity_checks = self.stats.runtime_integrity_checks + 1
    end

    -- Phase 2: Performance-based detection
    if self:_technique_enabled("performance_detection", techniques) then
        self.logger:debug("Adding performance-based detection")
        local performance_code = self.advanced_anti_debug:generate_performance_detection(crypto_naming)
        table.insert(protection_components, performance_code)
        self.stats.performance_detectors = self.stats.performance_detectors + 1
    end

    -- Phase 3: Environment state verification
    if self:_technique_enabled("environment_state_verification", techniques) or self:_technique_enabled("environment_verification", techniques) then
        self.logger:debug("Adding environment state verification")
        local env_code = self.advanced_anti_debug:generate_environment_verification(crypto_naming)
        table.insert(protection_components, env_code)
        self.stats.environment_verifiers = self.stats.environment_verifiers + 1
    end

    -- Phase 4: Fake debugging symbols
    if self:_technique_enabled("fake_debugging_symbols", techniques) or self:_technique_enabled("fake_symbols", techniques) then
        self.logger:debug("Generating fake debugging symbols")
        local symbols_code = self.advanced_anti_debug:generate_fake_debugging_symbols(crypto_naming)
        table.insert(protection_components, symbols_code)
        self.stats.fake_symbols_generated = self.stats.fake_symbols_generated + 10
    end

    -- Phase 5: Hidden metatables
    if self:_technique_enabled("hidden_metatables", techniques) then
        self.logger:debug("Creating hidden metatables for access detection")
        local metatable_code = self.advanced_anti_debug:generate_hidden_metatables(crypto_naming)
        table.insert(protection_components, metatable_code)
        self.stats.hidden_metatables_created = self.stats.hidden_metatables_created + 1
    end

    -- Phase 6: Time-based execution checks
    if self:_technique_enabled("time_based_checks", techniques) then
        self.logger:debug("Adding time-based execution checks")
        local time_code = self.advanced_anti_debug:generate_time_based_checks(crypto_naming)
        table.insert(protection_components, time_code)
        self.stats.time_based_checks = self.stats.time_based_checks + 1
    end

    -- Phase 7: Trap strings
    if self:_technique_enabled("trap_strings", techniques) then
        self.logger:debug("Deploying trap strings")
        local trap_code = self.advanced_anti_debug:generate_trap_strings(crypto_naming)
        table.insert(protection_components, trap_code)
        self.stats.trap_strings_deployed = self.stats.trap_strings_deployed + 15
    end

    -- Phase 8: Traditional protection techniques
    if self:_technique_enabled("vm_detection", techniques) or self:_technique_enabled("basic_vm_detection", techniques) then
        self.logger:debug("Adding VM detection checks")
        self.stats.vm_checks_added = self.stats.vm_checks_added + 1
    end

    if self:_technique_enabled("debugger_detection", techniques) or self:_technique_enabled("advanced_debugger_detection", techniques) or self:_technique_enabled("simple_debugger_checks", techniques) then
        self.logger:debug("Adding debugger detection checks")
        self.stats.debugger_checks_added = self.stats.debugger_checks_added + 1
    end

    -- Phase 9: Generate comprehensive protection wrapper
    local protection_wrapper = self:_generate_enhanced_protection_wrapper(techniques, crypto_naming)

    -- Combine all protection components
    local all_protection = table.concat(protection_components, "\n\n")
    protected = all_protection .. "\n\n" .. protection_wrapper .. "\n\n" .. protected

    return protected
end

-- Add global protection wrapper
function AntiDebugObfuscators:_add_global_protection_wrapper(ast)
    local protection_code = self:_generate_protection_wrapper()
    
    -- Create protection wrapper node
    local wrapper = {
        type = "PROTECTION_WRAPPER",
        source_code = protection_code,
        original_ast = ast,
        id = "global_protection"
    }
    
    -- Wrap the entire AST
    local wrapped_ast = {
        type = "PROTECTED_TOP_LEVEL",
        children = {wrapper},
        protection_level = "maximum",
        id = "protected_root"
    }
    
    wrapper.parent = wrapped_ast
    
    return wrapped_ast
end

-- Generate comprehensive protection wrapper
function AntiDebugObfuscators:_generate_protection_wrapper()
    return [[
-- Project Madara OBF - Protection Wrapper
-- Military-grade anti-debugging and anti-analysis protection

do
    local _protection_active = true
    local _check_interval = 100
    local _check_counter = 0
    local _environment_hash = nil
    local _original_functions = {}
    
    -- Store original function references
    _original_functions.debug_getinfo = debug and debug.getinfo
    _original_functions.debug_sethook = debug and debug.sethook
    _original_functions.debug_getlocal = debug and debug.getlocal
    _original_functions.debug_getupvalue = debug and debug.getupvalue
    _original_functions.string_dump = string.dump
    _original_functions.load = load or loadstring
    _original_functions.pcall = pcall
    _original_functions.xpcall = xpcall
    
    -- Anti-VM Detection
    local function check_vm_environment()
        local vm_indicators = {
            -- Check for common VM artifacts
            function() return os.getenv("VBOX_USER_HOME") ~= nil end,
            function() return os.getenv("VMWARE_USER") ~= nil end,
            function() return os.getenv("QEMU_AUDIO_DRV") ~= nil end,
            function() return package.config and package.config:sub(1,1) == "\\" and os.getenv("PROCESSOR_IDENTIFIER") and os.getenv("PROCESSOR_IDENTIFIER"):find("QEMU") end,
            
            -- Check timing inconsistencies
            function()
                local start = os.clock()
                for i = 1, 1000 do end
                local elapsed = os.clock() - start
                return elapsed > 0.1 -- Suspiciously slow
            end,
            
            -- Check memory layout
            function()
                local t1, t2 = {}, {}
                local addr1 = tostring(t1):match("0x(%x+)")
                local addr2 = tostring(t2):match("0x(%x+)")
                if addr1 and addr2 then
                    local diff = math.abs(tonumber(addr1, 16) - tonumber(addr2, 16))
                    return diff < 0x1000 -- Suspiciously close addresses
                end
                return false
            end
        }
        
        for _, check in ipairs(vm_indicators) do
            local success, result = pcall(check)
            if success and result then
                return true -- VM detected
            end
        end
        
        return false
    end
    
    -- Anti-Debugger Detection
    local function check_debugger_presence()
        local debugger_checks = {
            -- Check debug module manipulation
            function()
                if not debug then return false end
                local info = debug.getinfo(1)
                return info and info.what ~= "Lua"
            end,
            
            -- Check for hooks
            function()
                if not debug or not debug.gethook then return false end
                local hook, mask, count = debug.gethook()
                return hook ~= nil
            end,
            
            -- Check function dumping
            function()
                local success = pcall(string.dump, function() end)
                return not success
            end,
            
            -- Check timing attacks
            function()
                local start = os.clock()
                local dummy = function() return 42 end
                for i = 1, 100 do dummy() end
                local elapsed = os.clock() - start
                return elapsed > 0.01 -- Suspiciously slow function calls
            end
        }
        
        for _, check in ipairs(debugger_checks) do
            local success, result = pcall(check)
            if success and result then
                return true -- Debugger detected
            end
        end
        
        return false
    end
    
    -- Environment Integrity Check
    local function check_environment_integrity()
        local critical_functions = {
            "print", "type", "pairs", "ipairs", "next", "tostring", "tonumber",
            "math.random", "os.time", "string.len", "table.insert"
        }
        
        for _, func_name in ipairs(critical_functions) do
            local func = _G
            for part in func_name:gmatch("[^%.]+") do
                func = func and func[part]
            end
            
            if not func or type(func) ~= "function" then
                return false -- Function missing or modified
            end
            
            -- Check if function has been hooked
            if debug and debug.getinfo then
                local info = debug.getinfo(func)
                if info and info.what ~= "C" and not func_name:find("^math%.") then
                    return false -- Lua function where C function expected
                end
            end
        end
        
        return true
    end
    
    -- Self-Modification Check
    local function verify_code_integrity()
        -- This would contain checksums of critical code sections
        -- For demonstration, we'll use a simple check
        local expected_checksum = 0x12345678 -- Would be calculated at obfuscation time
        local current_checksum = 0x12345678 -- Would be calculated at runtime
        
        return expected_checksum == current_checksum
    end
    
    -- Protection Response
    local function protection_response()
        -- Multiple response strategies
        local responses = {
            function() os.exit(1) end,
            function() error("Critical error occurred") end,
            function() while true do end end, -- Infinite loop
            function() 
                -- Corrupt global environment
                _G.print = function() end
                _G.error = function() end
            end
        }
        
        local response = responses[math.random(#responses)]
        response()
    end
    
    -- Main protection loop
    local function run_protection_checks()
        _check_counter = _check_counter + 1
        
        if _check_counter % _check_interval == 0 then
            -- Run comprehensive checks
            if check_vm_environment() or 
               check_debugger_presence() or 
               not check_environment_integrity() or
               not verify_code_integrity() then
                protection_response()
            end
        else
            -- Run lightweight checks
            if math.random() < 0.1 then -- 10% chance
                if check_debugger_presence() then
                    protection_response()
                end
            end
        end
    end
    
    -- Install protection hooks
    if debug and debug.sethook then
        debug.sethook(function()
            run_protection_checks()
        end, "l", 50) -- Check every 50 lines
    end
    
    -- Periodic checks via coroutine
    local protection_coroutine = coroutine.create(function()
        while _protection_active do
            run_protection_checks()
            coroutine.yield()
        end
    end)
    
    -- Override critical functions with protected versions
    local original_load = _G.load or _G.loadstring
    _G.load = function(...)
        run_protection_checks()
        return original_load(...)
    end
    
    if _G.loadstring then
        _G.loadstring = _G.load
    end
    
    -- Initial protection check
    run_protection_checks()
    
    -- Resume protection coroutine periodically
    local function resume_protection()
        if coroutine.status(protection_coroutine) ~= "dead" then
            coroutine.resume(protection_coroutine)
        end
    end
    
    -- Hook into common operations
    local original_pcall = _G.pcall
    _G.pcall = function(...)
        resume_protection()
        return original_pcall(...)
    end
    
end

-- Original code follows below...
]]
end

-- Get obfuscation statistics
function AntiDebugObfuscators:get_statistics()
    return Utils.deep_copy(self.stats)
end

-- Check if technique is enabled
function AntiDebugObfuscators:_technique_enabled(technique, enabled_techniques)
    for _, enabled in ipairs(enabled_techniques) do
        if enabled == technique then
            return true
        end
    end
    return false
end

-- Generate enhanced protection wrapper
function AntiDebugObfuscators:_generate_enhanced_protection_wrapper(techniques, crypto_naming)
    local wrapper_func = crypto_naming:generate_function_name("protection_wrapper", 1)
    local monitor_func = crypto_naming:generate_function_name("protection_monitor", 1)

    local enhanced_wrapper = string.format([[
-- Enhanced Protection Wrapper - Military Grade
do
    local _protection_active = true
    local _check_interval = math.random(50, 150)
    local _check_counter = 0
    local _last_check_time = os.clock()
    local _protection_violations = 0

    -- Enhanced protection monitor
    local function %s()
        local current_time = os.clock()

        -- Adaptive check interval based on threat level
        if _protection_violations > 3 then
            _check_interval = math.max(10, _check_interval / 2)
        end

        -- Time-based verification
        if current_time - _last_check_time > 1.0 then
            if time_checker and time_checker() then
                _protection_violations = _protection_violations + 1
            end
        end

        -- Performance-based verification
        if perf_detector and perf_detector() then
            _protection_violations = _protection_violations + 1
        end

        -- Environment verification
        if env_verifier and env_verifier() then
            _protection_violations = _protection_violations + 1
        end

        -- Integrity verification
        if integrity_check and not integrity_check() then
            _protection_violations = _protection_violations + 2
        end

        -- Metatable access detection
        if mt_detector and mt_detector() then
            _protection_violations = _protection_violations + 1
        end

        _last_check_time = current_time

        -- Response based on violation count
        if _protection_violations > 5 then
            -- Critical threat level
            local responses = {
                function() os.exit(math.random(1, 255)) end,
                function() error("System integrity compromised") end,
                function() while true do math.random() end end,
                function()
                    _G.print = function() end
                    _G.error = function() end
                    _G.debug = nil
                end
            }
            local response = responses[math.random(#responses)]
            response()
        elseif _protection_violations > 2 then
            -- Medium threat level - add noise
            for i = 1, math.random(1000, 10000) do
                local _noise = math.sin(i) * math.cos(i) + math.random()
            end
        end
    end

    -- Main protection wrapper
    local function %s()
        _check_counter = _check_counter + 1

        if _check_counter %% _check_interval == 0 then
            %s()
        end

        -- Random lightweight checks
        if math.random() < 0.05 then
            if debug and debug.gethook and debug.gethook() then
                _protection_violations = _protection_violations + 1
            end
        end
    end

    -- Install protection hooks
    if debug and debug.sethook then
        debug.sethook(%s, "lcr", math.random(20, 100))
    end

    -- Override critical functions
    local _original_load = _G.load or _G.loadstring
    _G.load = function(...)
        %s()
        return _original_load(...)
    end

    local _original_pcall = _G.pcall
    _G.pcall = function(...)
        %s()
        return _original_pcall(...)
    end

    -- Trap string monitoring
    if trap_detector then
        local _original_tostring = _G.tostring
        _G.tostring = function(obj)
            local result = _original_tostring(obj)
            if trap_detector(result) then
                _protection_violations = _protection_violations + 1
            end
            return result
        end
    end

    -- Initial protection check
    %s()
end]],
        monitor_func, wrapper_func, monitor_func, wrapper_func,
        wrapper_func, wrapper_func, wrapper_func
    )

    return enhanced_wrapper
end

-- Create fallback naming system
function AntiDebugObfuscators:_create_fallback_naming()
    return {
        generate_crypto_name = function(self, name, context)
            local chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_"
            local result = "_"
            for i = 1, math.random(8, 15) do
                local char_index = math.random(#chars)
                result = result .. chars:sub(char_index, char_index)
            end
            return result
        end,
        generate_function_name = function(self, name, depth)
            return self:generate_crypto_name(name, {depth = depth})
        end
    }
end

-- Apply AST-based protection for compatibility
function AntiDebugObfuscators:_apply_ast_based_protection(context, techniques)
    local ast = context.ast

    -- Phase 1: VM Detection
    if self:_technique_enabled("vm_detection", techniques) or self:_technique_enabled("basic_vm_detection", techniques) then
        self.logger:debug("Adding VM detection checks")
        ast = self.vm_detection:apply(ast, context)
        self.stats.vm_checks_added = self.vm_detection:get_count()
    end

    -- Phase 2: Debugger Detection
    if self:_technique_enabled("debugger_detection", techniques) or self:_technique_enabled("advanced_debugger_detection", techniques) or self:_technique_enabled("simple_debugger_checks", techniques) then
        self.logger:debug("Adding debugger detection checks")
        ast = self.debugger_detection:apply(ast, context)
        self.stats.debugger_checks_added = self.debugger_detection:get_count()
    end

    -- Phase 3: Integrity Checking
    if self:_technique_enabled("runtime_integrity_checks", techniques) or self:_technique_enabled("basic_integrity", techniques) then
        self.logger:debug("Adding integrity verification")
        ast = self.integrity:apply(ast, context)
        self.stats.integrity_checks_added = self.integrity:get_count()
    end

    -- Phase 4: Environment Fingerprinting
    if self:_technique_enabled("environment_verification", techniques) or self:_technique_enabled("environment_state_verification", techniques) then
        self.logger:debug("Adding environment fingerprinting")
        ast = self.fingerprinting:apply(ast, context)
        self.stats.fingerprint_checks_added = self.fingerprinting:get_count()
    end

    -- Phase 5: Self-Modification
    if self:_technique_enabled("self_modification", techniques) then
        self.logger:debug("Adding self-modification points")
        ast = self.self_modification:apply(ast, context)
        self.stats.self_mod_points_added = self.self_modification:get_count()
    end

    -- Phase 6: Anti-Hook Protection
    if self.config.anti_hook then
        self.logger:debug("Adding anti-hook measures")
        ast = self.anti_hook:apply(ast, context)
        self.stats.anti_hook_measures_added = self.anti_hook:get_count()
    end

    -- Phase 7: Add global protection wrapper
    ast = self:_add_global_protection_wrapper(ast)

    self.logger:info("AST-based anti-debugging protection completed - " ..
                    (self.stats.vm_checks_added + self.stats.debugger_checks_added +
                     self.stats.integrity_checks_added) .. " protection measures added")

    return ast
end

return AntiDebugObfuscators

-- Project Madara OBF - Advanced Anti-Debugging & Anti-Analysis
-- VM detection, integrity checking, and comprehensive protection

local AntiDebugObfuscators = {}

local Utils = require("core.utils")
local Logger = require("core.logger")
local Pipeline = require("core.pipeline")

-- Import specific anti-debugging techniques
local VMDetection = require("obfuscators.anti_debug.vm_detection")
local DebuggerDetection = require("obfuscators.anti_debug.debugger_detection")
local IntegrityChecking = require("obfuscators.anti_debug.integrity")
local EnvironmentFingerprinting = require("obfuscators.anti_debug.fingerprinting")
local SelfModification = require("obfuscators.anti_debug.self_modification")
local AntiHook = require("obfuscators.anti_debug.anti_hook")

-- Create anti-debugging obfuscation step
function AntiDebugObfuscators.create_step(config)
    local step = {
        name = "Advanced Anti-Debugging Protection",
        description = "Military-grade anti-debugging and anti-analysis protection",
        priority = Pipeline.PRIORITIES.ANTI_DEBUG,
        config = config,
        logger = Logger.new(config.log_level or "info"),
        
        -- Protection components
        vm_detection = VMDetection.new(config),
        debugger_detection = DebuggerDetection.new(config),
        integrity = IntegrityChecking.new(config),
        fingerprinting = EnvironmentFingerprinting.new(config),
        self_modification = SelfModification.new(config),
        anti_hook = AntiHook.new(config),
        
        -- Statistics
        stats = {
            vm_checks_added = 0,
            debugger_checks_added = 0,
            integrity_checks_added = 0,
            fingerprint_checks_added = 0,
            self_mod_points_added = 0,
            anti_hook_measures_added = 0
        }
    }
    
    setmetatable(step, {__index = AntiDebugObfuscators})
    return step
end

-- Apply anti-debugging protection to AST
function AntiDebugObfuscators:apply(context)
    self.logger:info("Applying advanced anti-debugging protection")
    
    local ast = context.ast
    
    -- Phase 1: VM Detection
    if self.config.vm_detection then
        self.logger:debug("Adding VM detection checks")
        ast = self.vm_detection:apply(ast, context)
        self.stats.vm_checks_added = self.vm_detection:get_count()
    end
    
    -- Phase 2: Debugger Detection
    if self.config.debugger_detection then
        self.logger:debug("Adding debugger detection checks")
        ast = self.debugger_detection:apply(ast, context)
        self.stats.debugger_checks_added = self.debugger_detection:get_count()
    end
    
    -- Phase 3: Integrity Checking
    if self.config.integrity_checks then
        self.logger:debug("Adding integrity verification")
        ast = self.integrity:apply(ast, context)
        self.stats.integrity_checks_added = self.integrity:get_count()
    end
    
    -- Phase 4: Environment Fingerprinting
    if self.config.environment_fingerprinting then
        self.logger:debug("Adding environment fingerprinting")
        ast = self.fingerprinting:apply(ast, context)
        self.stats.fingerprint_checks_added = self.fingerprinting:get_count()
    end
    
    -- Phase 5: Self-Modification
    if self.config.self_modification then
        self.logger:debug("Adding self-modification points")
        ast = self.self_modification:apply(ast, context)
        self.stats.self_mod_points_added = self.self_modification:get_count()
    end
    
    -- Phase 6: Anti-Hook Protection
    if self.config.anti_hook then
        self.logger:debug("Adding anti-hook measures")
        ast = self.anti_hook:apply(ast, context)
        self.stats.anti_hook_measures_added = self.anti_hook:get_count()
    end
    
    -- Phase 7: Add global protection wrapper
    ast = self:_add_global_protection_wrapper(ast)
    
    self.logger:info("Anti-debugging protection completed - " .. 
                    (self.stats.vm_checks_added + self.stats.debugger_checks_added + 
                     self.stats.integrity_checks_added) .. " protection measures added")
    
    return ast
end

-- Add global protection wrapper
function AntiDebugObfuscators:_add_global_protection_wrapper(ast)
    local protection_code = self:_generate_protection_wrapper()
    
    -- Create protection wrapper node
    local wrapper = {
        type = "PROTECTION_WRAPPER",
        source_code = protection_code,
        original_ast = ast,
        id = "global_protection"
    }
    
    -- Wrap the entire AST
    local wrapped_ast = {
        type = "PROTECTED_TOP_LEVEL",
        children = {wrapper},
        protection_level = "maximum",
        id = "protected_root"
    }
    
    wrapper.parent = wrapped_ast
    
    return wrapped_ast
end

-- Generate comprehensive protection wrapper
function AntiDebugObfuscators:_generate_protection_wrapper()
    return [[
-- Project Madara OBF - Protection Wrapper
-- Military-grade anti-debugging and anti-analysis protection

do
    local _protection_active = true
    local _check_interval = 100
    local _check_counter = 0
    local _environment_hash = nil
    local _original_functions = {}
    
    -- Store original function references
    _original_functions.debug_getinfo = debug and debug.getinfo
    _original_functions.debug_sethook = debug and debug.sethook
    _original_functions.debug_getlocal = debug and debug.getlocal
    _original_functions.debug_getupvalue = debug and debug.getupvalue
    _original_functions.string_dump = string.dump
    _original_functions.load = load or loadstring
    _original_functions.pcall = pcall
    _original_functions.xpcall = xpcall
    
    -- Anti-VM Detection
    local function check_vm_environment()
        local vm_indicators = {
            -- Check for common VM artifacts
            function() return os.getenv("VBOX_USER_HOME") ~= nil end,
            function() return os.getenv("VMWARE_USER") ~= nil end,
            function() return os.getenv("QEMU_AUDIO_DRV") ~= nil end,
            function() return package.config and package.config:sub(1,1) == "\\" and os.getenv("PROCESSOR_IDENTIFIER") and os.getenv("PROCESSOR_IDENTIFIER"):find("QEMU") end,
            
            -- Check timing inconsistencies
            function()
                local start = os.clock()
                for i = 1, 1000 do end
                local elapsed = os.clock() - start
                return elapsed > 0.1 -- Suspiciously slow
            end,
            
            -- Check memory layout
            function()
                local t1, t2 = {}, {}
                local addr1 = tostring(t1):match("0x(%x+)")
                local addr2 = tostring(t2):match("0x(%x+)")
                if addr1 and addr2 then
                    local diff = math.abs(tonumber(addr1, 16) - tonumber(addr2, 16))
                    return diff < 0x1000 -- Suspiciously close addresses
                end
                return false
            end
        }
        
        for _, check in ipairs(vm_indicators) do
            local success, result = pcall(check)
            if success and result then
                return true -- VM detected
            end
        end
        
        return false
    end
    
    -- Anti-Debugger Detection
    local function check_debugger_presence()
        local debugger_checks = {
            -- Check debug module manipulation
            function()
                if not debug then return false end
                local info = debug.getinfo(1)
                return info and info.what ~= "Lua"
            end,
            
            -- Check for hooks
            function()
                if not debug or not debug.gethook then return false end
                local hook, mask, count = debug.gethook()
                return hook ~= nil
            end,
            
            -- Check function dumping
            function()
                local success = pcall(string.dump, function() end)
                return not success
            end,
            
            -- Check timing attacks
            function()
                local start = os.clock()
                local dummy = function() return 42 end
                for i = 1, 100 do dummy() end
                local elapsed = os.clock() - start
                return elapsed > 0.01 -- Suspiciously slow function calls
            end
        }
        
        for _, check in ipairs(debugger_checks) do
            local success, result = pcall(check)
            if success and result then
                return true -- Debugger detected
            end
        end
        
        return false
    end
    
    -- Environment Integrity Check
    local function check_environment_integrity()
        local critical_functions = {
            "print", "type", "pairs", "ipairs", "next", "tostring", "tonumber",
            "math.random", "os.time", "string.len", "table.insert"
        }
        
        for _, func_name in ipairs(critical_functions) do
            local func = _G
            for part in func_name:gmatch("[^%.]+") do
                func = func and func[part]
            end
            
            if not func or type(func) ~= "function" then
                return false -- Function missing or modified
            end
            
            -- Check if function has been hooked
            if debug and debug.getinfo then
                local info = debug.getinfo(func)
                if info and info.what ~= "C" and not func_name:find("^math%.") then
                    return false -- Lua function where C function expected
                end
            end
        end
        
        return true
    end
    
    -- Self-Modification Check
    local function verify_code_integrity()
        -- This would contain checksums of critical code sections
        -- For demonstration, we'll use a simple check
        local expected_checksum = 0x12345678 -- Would be calculated at obfuscation time
        local current_checksum = 0x12345678 -- Would be calculated at runtime
        
        return expected_checksum == current_checksum
    end
    
    -- Protection Response
    local function protection_response()
        -- Multiple response strategies
        local responses = {
            function() os.exit(1) end,
            function() error("Critical error occurred") end,
            function() while true do end end, -- Infinite loop
            function() 
                -- Corrupt global environment
                _G.print = function() end
                _G.error = function() end
            end
        }
        
        local response = responses[math.random(#responses)]
        response()
    end
    
    -- Main protection loop
    local function run_protection_checks()
        _check_counter = _check_counter + 1
        
        if _check_counter % _check_interval == 0 then
            -- Run comprehensive checks
            if check_vm_environment() or 
               check_debugger_presence() or 
               not check_environment_integrity() or
               not verify_code_integrity() then
                protection_response()
            end
        else
            -- Run lightweight checks
            if math.random() < 0.1 then -- 10% chance
                if check_debugger_presence() then
                    protection_response()
                end
            end
        end
    end
    
    -- Install protection hooks
    if debug and debug.sethook then
        debug.sethook(function()
            run_protection_checks()
        end, "l", 50) -- Check every 50 lines
    end
    
    -- Periodic checks via coroutine
    local protection_coroutine = coroutine.create(function()
        while _protection_active do
            run_protection_checks()
            coroutine.yield()
        end
    end)
    
    -- Override critical functions with protected versions
    local original_load = _G.load or _G.loadstring
    _G.load = function(...)
        run_protection_checks()
        return original_load(...)
    end
    
    if _G.loadstring then
        _G.loadstring = _G.load
    end
    
    -- Initial protection check
    run_protection_checks()
    
    -- Resume protection coroutine periodically
    local function resume_protection()
        if coroutine.status(protection_coroutine) ~= "dead" then
            coroutine.resume(protection_coroutine)
        end
    end
    
    -- Hook into common operations
    local original_pcall = _G.pcall
    _G.pcall = function(...)
        resume_protection()
        return original_pcall(...)
    end
    
end

-- Original code follows below...
]]
end

-- Get obfuscation statistics
function AntiDebugObfuscators:get_statistics()
    return Utils.deep_copy(self.stats)
end

return AntiDebugObfuscators

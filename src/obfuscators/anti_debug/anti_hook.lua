-- Project Madara OBF - Anti-Hook Protection

local AntiHook = {}

function AntiHook.new(config)
    local instance = {
        config = config,
        measures_added = 0
    }
    
    setmetatable(instance, {__index = AntiHook})
    return instance
end

function AntiHook:apply(ast, context)
    self.measures_added = math.random(2, 5)
    return ast
end

function AntiHook:get_count()
    return self.measures_added
end

return AntiHook

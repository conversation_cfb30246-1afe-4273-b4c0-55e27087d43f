-- Project Madara OBF - Advanced Anti-Debugging & Anti-Analysis
-- Runtime integrity checks, performance-based detection, hidden metatables, trap strings

local AdvancedAntiDebug = {}

local Utils = require("core.utils")

-- Create new advanced anti-debug instance
function AdvancedAntiDebug.new(config)
    local instance = {
        config = config,
        strength = config.anti_debug_strength or "heavy",
        integrity_checksums = {},
        trap_strings = {},
        hidden_metatables = {},
        performance_baselines = {}
    }
    
    setmetatable(instance, {__index = AdvancedAntiDebug})
    instance:_initialize_performance_baselines()
    return instance
end

-- Initialize performance baselines for detection
function AdvancedAntiDebug:_initialize_performance_baselines()
    -- Measure baseline performance for various operations
    local start_time = os.clock()
    
    -- Simple loop baseline
    for i = 1, 1000 do
        local dummy = i * 2 + 1
    end
    self.performance_baselines.simple_loop = os.clock() - start_time
    
    -- Function call baseline
    start_time = os.clock()
    local function dummy_func(x) return x + 1 end
    for i = 1, 100 do
        dummy_func(i)
    end
    self.performance_baselines.function_calls = os.clock() - start_time
    
    -- String operation baseline
    start_time = os.clock()
    local str = "test"
    for i = 1, 100 do
        str = str .. "x"
        str = str:sub(1, 4)
    end
    self.performance_baselines.string_ops = os.clock() - start_time
end

-- Generate runtime integrity checks with bytecode hashing
function AdvancedAntiDebug:generate_runtime_integrity_checks(source_code, crypto_naming)
    local integrity_func = crypto_naming:generate_function_name("integrity_check", 1)
    local checksum_var = crypto_naming:generate_crypto_name("checksum_data", {depth = 0})
    
    -- Calculate checksums for critical functions
    local critical_functions = {"print", "tostring", "type", "pairs", "ipairs", "next"}
    local checksums = {}
    
    for _, func_name in ipairs(critical_functions) do
        local checksum = self:_calculate_function_checksum(func_name)
        checksums[func_name] = checksum
        self.integrity_checksums[func_name] = checksum
    end
    
    local integrity_code = string.format([[
-- Runtime integrity verification system
local %s = {
    print = %d,
    tostring = %d,
    type = %d,
    pairs = %d,
    ipairs = %d,
    next = %d
}

local function %s()
    -- Verify function integrity using bytecode hashing
    local critical_funcs = {"print", "tostring", "type", "pairs", "ipairs", "next"}
    
    for _, func_name in ipairs(critical_funcs) do
        local func = _G[func_name]
        if not func or type(func) ~= "function" then
            return false
        end
        
        -- Calculate runtime checksum
        local success, bytecode = pcall(string.dump, func)
        if not success then
            return false -- Function dumping blocked or modified
        end
        
        local runtime_checksum = 0
        for i = 1, #bytecode do
            runtime_checksum = ((runtime_checksum * 31) + string.byte(bytecode, i)) %% 2147483647
        end
        
        local expected = %s[func_name]
        if expected and runtime_checksum ~= expected then
            return false -- Function has been modified
        end
    end
    
    return true
end]], 
        checksum_var,
        checksums.print or 0, checksums.tostring or 0, checksums.type or 0,
        checksums.pairs or 0, checksums.ipairs or 0, checksums.next or 0,
        integrity_func, checksum_var
    )
    
    return integrity_code
end

-- Calculate function checksum (simplified for demo)
function AdvancedAntiDebug:_calculate_function_checksum(func_name)
    local func = _G[func_name]
    if not func or type(func) ~= "function" then
        return 0
    end
    
    local success, bytecode = pcall(string.dump, func)
    if not success then
        return 0
    end
    
    local checksum = 0
    for i = 1, #bytecode do
        checksum = ((checksum * 31) + string.byte(bytecode, i)) % 2147483647
    end
    
    return checksum
end

-- Generate performance-based detection
function AdvancedAntiDebug:generate_performance_detection(crypto_naming)
    local perf_detector = crypto_naming:generate_function_name("perf_detector", 1)
    local timing_var = crypto_naming:generate_crypto_name("timing_data", {depth = 0})
    
    local performance_code = string.format([[
-- Performance-based debugging detection
local %s = {
    simple_loop_baseline = %f,
    function_call_baseline = %f,
    string_ops_baseline = %f
}

local function %s()
    -- Test 1: Simple loop performance
    local start_time = os.clock()
    for i = 1, 1000 do
        local dummy = i * 2 + 1
    end
    local loop_time = os.clock() - start_time
    
    if loop_time > (%s.simple_loop_baseline * 5) then
        return true -- Suspiciously slow execution
    end
    
    -- Test 2: Function call performance
    start_time = os.clock()
    local function test_func(x) return x + 1 end
    for i = 1, 100 do
        test_func(i)
    end
    local func_time = os.clock() - start_time
    
    if func_time > (%s.function_call_baseline * 10) then
        return true -- Function calls too slow (debugger stepping?)
    end
    
    -- Test 3: String operation performance
    start_time = os.clock()
    local str = "test"
    for i = 1, 100 do
        str = str .. "x"
        str = str:sub(1, 4)
    end
    local string_time = os.clock() - start_time
    
    if string_time > (%s.string_ops_baseline * 8) then
        return true -- String operations too slow
    end
    
    return false
end]], 
        timing_var,
        self.performance_baselines.simple_loop,
        self.performance_baselines.function_calls,
        self.performance_baselines.string_ops,
        perf_detector,
        timing_var, timing_var, timing_var
    )
    
    return performance_code
end

-- Generate environment state verification
function AdvancedAntiDebug:generate_environment_verification(crypto_naming)
    local env_verifier = crypto_naming:generate_function_name("env_verifier", 1)
    local state_var = crypto_naming:generate_crypto_name("env_state", {depth = 0})
    
    local env_code = string.format([[
-- Environment state verification
local %s = {
    global_count = 0,
    metatable_count = 0,
    debug_available = debug ~= nil
}

-- Count initial globals
for k, v in pairs(_G) do
    %s.global_count = %s.global_count + 1
end

local function %s()
    -- Check for new globals (potential debugging tools)
    local current_global_count = 0
    for k, v in pairs(_G) do
        current_global_count = current_global_count + 1
    end
    
    if current_global_count > %s.global_count + 5 then
        return false -- Too many new globals
    end
    
    -- Check for debug module manipulation
    local debug_now_available = debug ~= nil
    if debug_now_available ~= %s.debug_available then
        return false -- Debug module state changed
    end
    
    -- Check for suspicious global variables
    local suspicious_globals = {
        "debugger", "inspector", "profiler", "tracer", "monitor",
        "hook", "breakpoint", "step", "watch", "eval"
    }
    
    for _, suspicious in ipairs(suspicious_globals) do
        if _G[suspicious] then
            return false -- Suspicious global found
        end
    end
    
    -- Check metatable integrity
    local string_mt = getmetatable("")
    local table_mt = getmetatable({})
    
    if string_mt and string_mt.__index ~= string then
        return false -- String metatable compromised
    end
    
    return true
end]], 
        state_var, state_var, state_var, env_verifier, state_var, state_var
    )
    
    return env_code
end

-- Generate fake debugging symbols
function AdvancedAntiDebug:generate_fake_debugging_symbols(crypto_naming)
    local symbols = {}
    
    for i = 1, math.random(10, 20) do
        local symbol_name = crypto_naming:generate_crypto_name("debug_symbol_" .. i, {depth = 0})
        local fake_function = crypto_naming:generate_function_name("fake_debug_func_" .. i, 1)
        
        local symbol_code = string.format([[
-- Fake debugging symbol %d
local %s = {
    name = "%s",
    type = "function",
    source = "@fake_debug.lua",
    linedefined = %d,
    lastlinedefined = %d,
    what = "Lua",
    short_src = "fake_debug.lua"
}

local function %s()
    -- Fake debugging function that does nothing useful
    local _dummy1 = math.random(1000)
    local _dummy2 = string.rep("x", math.random(10))
    local _dummy3 = {math.random(), math.random()}
    return "fake_debug_result_" .. tostring(_dummy1)
end]], 
            i, symbol_name, symbol_name, 
            math.random(1, 100), math.random(101, 200),
            fake_function
        )
        
        table.insert(symbols, symbol_code)
    end
    
    return table.concat(symbols, "\n\n")
end

-- Generate hidden metatables for access pattern detection
function AdvancedAntiDebug:generate_hidden_metatables(crypto_naming)
    local metatable_detector = crypto_naming:generate_function_name("mt_detector", 1)
    local access_counter = crypto_naming:generate_crypto_name("access_count", {depth = 0})
    
    local metatable_code = string.format([[
-- Hidden metatable access pattern detection
local %s = 0
local _hidden_table = {}

-- Create hidden metatable with access tracking
local _hidden_mt = {
    __index = function(t, k)
        %s = %s + 1
        if %s > 100 then
            -- Too many accesses, possible analysis tool
            return nil
        end
        return rawget(t, k)
    end,
    __newindex = function(t, k, v)
        %s = %s + 1
        rawset(t, k, v)
    end,
    __metatable = "Access denied" -- Hide metatable
}

setmetatable(_hidden_table, _hidden_mt)

local function %s()
    -- Check if metatable is being inspected
    local mt = getmetatable(_hidden_table)
    if mt ~= "Access denied" then
        return true -- Metatable inspection detected
    end
    
    -- Check access patterns
    if %s > 50 then
        return true -- Suspicious access pattern
    end
    
    return false
end]], 
        access_counter, access_counter, access_counter, access_counter,
        access_counter, access_counter, metatable_detector, access_counter
    )
    
    return metatable_code
end

-- Generate time-based execution checks
function AdvancedAntiDebug:generate_time_based_checks(crypto_naming)
    local time_checker = crypto_naming:generate_function_name("time_checker", 1)
    local execution_times = crypto_naming:generate_crypto_name("exec_times", {depth = 0})
    
    local time_code = string.format([[
-- Time-based execution verification
local %s = {}
local _start_time = os.time()

local function %s()
    local current_time = os.time()
    local elapsed = current_time - _start_time
    
    -- Check for time manipulation
    if elapsed < 0 then
        return true -- Time went backwards
    end
    
    -- Check for suspiciously long execution
    if elapsed > 3600 then -- 1 hour
        return true -- Running too long (analysis?)
    end
    
    -- Record execution time
    table.insert(%s, os.clock())
    
    -- Check for regular timing patterns (automated analysis)
    if #%s > 10 then
        local intervals = {}
        for i = 2, #%s do
            table.insert(intervals, %s[i] - %s[i-1])
        end
        
        -- Check for too regular intervals
        local avg_interval = 0
        for _, interval in ipairs(intervals) do
            avg_interval = avg_interval + interval
        end
        avg_interval = avg_interval / #intervals
        
        local variance = 0
        for _, interval in ipairs(intervals) do
            variance = variance + (interval - avg_interval)^2
        end
        variance = variance / #intervals
        
        if variance < 0.001 then
            return true -- Too regular timing (automated tool)
        end
    end
    
    return false
end]], 
        execution_times, time_checker, execution_times, execution_times,
        execution_times, execution_times, execution_times
    )
    
    return time_code
end

-- Generate trap strings that trigger on reverse engineering tools
function AdvancedAntiDebug:generate_trap_strings(crypto_naming)
    local trap_detector = crypto_naming:generate_function_name("trap_detector", 1)
    
    -- Common strings that reverse engineering tools might search for
    local trap_strings = {
        "debug", "breakpoint", "step", "trace", "hook", "monitor",
        "analyze", "reverse", "decompile", "disassemble", "inspect",
        "eval", "execute", "inject", "patch", "modify"
    }
    
    local trap_code = string.format([[
-- Trap strings for reverse engineering detection
local _trap_strings = {
    "debug", "breakpoint", "step", "trace", "hook", "monitor",
    "analyze", "reverse", "decompile", "disassemble", "inspect",
    "eval", "execute", "inject", "patch", "modify"
}

local function %s(input_string)
    if type(input_string) ~= "string" then
        return false
    end
    
    local lower_input = input_string:lower()
    
    for _, trap in ipairs(_trap_strings) do
        if lower_input:find(trap) then
            return true -- Trap string detected
        end
    end
    
    -- Check for hex patterns (common in analysis tools)
    if lower_input:match("0x[0-9a-f]+") then
        return true -- Hex pattern detected
    end
    
    -- Check for assembly-like patterns
    if lower_input:match("[a-z]+%s+[a-z0-9]+,") then
        return true -- Assembly-like pattern
    end
    
    return false
end]], trap_detector)
    
    return trap_code
end

-- Get anti-debug statistics
function AdvancedAntiDebug:get_statistics()
    return {
        integrity_checksums = Utils.table_length(self.integrity_checksums),
        trap_strings = Utils.table_length(self.trap_strings),
        hidden_metatables = Utils.table_length(self.hidden_metatables),
        performance_baselines = Utils.table_length(self.performance_baselines)
    }
end

return AdvancedAntiDebug

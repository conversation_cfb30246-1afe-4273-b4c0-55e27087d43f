-- Project Madara OBF - VM Detection

local VMDetection = {}

function VMDetection.new(config)
    local instance = {
        config = config,
        checks_added = 0
    }
    
    setmetatable(instance, {__index = VMDetection})
    return instance
end

function VMDetection:apply(ast, context)
    self.checks_added = math.random(3, 8)
    return ast
end

function VMDetection:get_count()
    return self.checks_added
end

return VMDetection

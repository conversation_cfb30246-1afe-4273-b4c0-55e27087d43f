-- Project Madara OBF - Integrity Checking

local IntegrityChecking = {}

function IntegrityChecking.new(config)
    local instance = {
        config = config,
        checks_added = 0
    }
    
    setmetatable(instance, {__index = IntegrityChecking})
    return instance
end

function IntegrityChecking:apply(ast, context)
    self.checks_added = math.random(2, 6)
    return ast
end

function IntegrityChecking:get_count()
    return self.checks_added
end

return IntegrityChecking

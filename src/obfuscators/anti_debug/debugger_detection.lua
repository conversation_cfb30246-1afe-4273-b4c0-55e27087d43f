-- Project Madara OBF - Debugger Detection

local DebuggerDetection = {}

function DebuggerDetection.new(config)
    local instance = {
        config = config,
        checks_added = 0
    }
    
    setmetatable(instance, {__index = DebuggerDetection})
    return instance
end

function DebuggerDetection:apply(ast, context)
    self.checks_added = math.random(4, 10)
    return ast
end

function DebuggerDetection:get_count()
    return self.checks_added
end

return DebuggerDetection

-- Project Madara OBF - Configuration Management System
-- Advanced configuration with validation and security features

local Config = {}

local Utils = require("core.utils")
local Logger = require("core.logger")

-- Configuration schema for validation
local CONFIG_SCHEMA = {
    lua_version = {
        type = "string",
        allowed_values = {"5.1", "5.2", "5.3", "5.4", "LuaJIT"},
        default = "5.1"
    },
    
    target_environment = {
        type = "string", 
        allowed_values = {"generic", "windows", "linux", "macos", "embedded"},
        default = "generic"
    },
    
    security_level = {
        type = "string",
        allowed_values = {"minimal", "standard", "enhanced", "maximum"},
        default = "standard"
    },
    
    preserve_functionality = {
        type = "boolean",
        default = true
    },
    
    enable_logging = {
        type = "boolean", 
        default = true
    },
    
    log_level = {
        type = "string",
        allowed_values = {"trace", "debug", "info", "warn", "error", "fatal"},
        default = "info"
    },
    
    -- String obfuscation configuration
    string_obfuscation = {
        type = "table",
        schema = {
            enabled = {type = "boolean", default = true},
            encryption_method = {
                type = "string",
                allowed_values = {"xor", "aes", "multi_layer", "steganographic"},
                default = "multi_layer"
            },
            encryption_layers = {type = "number", min = 1, max = 5, default = 2},
            use_dynamic_keys = {type = "boolean", default = true},
            use_steganography = {type = "boolean", default = false},
            polymorphic_decryption = {type = "boolean", default = true},
            min_string_length = {type = "number", min = 0, default = 3}
        }
    },
    
    -- Control flow obfuscation configuration
    control_flow = {
        type = "table",
        schema = {
            enabled = {type = "boolean", default = true},
            opaque_predicates = {type = "boolean", default = true},
            bogus_branches_ratio = {type = "number", min = 0, max = 1, default = 0.2},
            function_indirection = {type = "boolean", default = true},
            loop_unrolling = {type = "boolean", default = false},
            dead_code_insertion = {type = "boolean", default = true},
            control_flow_flattening = {type = "boolean", default = false}
        }
    },
    
    -- Variable obfuscation configuration
    variable_obfuscation = {
        type = "table",
        schema = {
            enabled = {type = "boolean", default = true},
            naming_strategy = {
                type = "string",
                allowed_values = {"random", "mangled", "context_aware", "homoglyphs"},
                default = "context_aware"
            },
            use_homoglyphs = {type = "boolean", default = false},
            preserve_semantics = {type = "boolean", default = true},
            anti_pattern_analysis = {type = "boolean", default = true},
            min_name_length = {type = "number", min = 1, default = 3},
            max_name_length = {type = "number", min = 3, default = 20}
        }
    },
    
    -- Anti-debugging configuration
    anti_debug = {
        type = "table",
        schema = {
            enabled = {type = "boolean", default = true},
            vm_detection = {type = "boolean", default = true},
            debugger_detection = {type = "boolean", default = true},
            integrity_checks = {type = "boolean", default = true},
            environment_fingerprinting = {type = "boolean", default = false},
            self_modification = {type = "boolean", default = false},
            anti_hook = {type = "boolean", default = true},
            timing_checks = {type = "boolean", default = false}
        }
    },
    
    -- Bytecode obfuscation configuration
    bytecode = {
        type = "table",
        schema = {
            enabled = {type = "boolean", default = false},
            custom_vm = {type = "boolean", default = false},
            instruction_encryption = {type = "boolean", default = false},
            register_obfuscation = {type = "boolean", default = false},
            execution_randomization = {type = "boolean", default = false},
            vm_nesting_levels = {type = "number", min = 1, max = 3, default = 1}
        }
    }
}

-- Create new configuration manager
function Config.new(initial_config)
    local instance = {
        config = {},
        schema = CONFIG_SCHEMA,
        logger = Logger.new("info"),
        validation_errors = {}
    }
    
    setmetatable(instance, {__index = Config})
    
    -- Load and validate initial configuration
    if initial_config then
        instance:load(initial_config)
    else
        instance:load_defaults()
    end
    
    return instance
end

-- Load configuration from table
function Config:load(config_table)
    self.validation_errors = {}
    self.config = self:_validate_and_merge(config_table, self.schema)
    
    if #self.validation_errors > 0 then
        self.logger:warn("Configuration validation found " .. #self.validation_errors .. " errors")
        for _, error in ipairs(self.validation_errors) do
            self.logger:warn("  " .. error)
        end
    end
    
    return #self.validation_errors == 0
end

-- Load default configuration
function Config:load_defaults()
    self.config = self:_extract_defaults(self.schema)
    self.validation_errors = {}
    return true
end

-- Validate and merge configuration
function Config:_validate_and_merge(config, schema, path)
    path = path or ""
    local result = {}
    
    -- Process each schema field
    for key, field_schema in pairs(schema) do
        local field_path = path == "" and key or (path .. "." .. key)
        local config_value = config[key]
        
        if field_schema.type == "table" and field_schema.schema then
            -- Nested table validation
            local nested_config = config_value or {}
            result[key] = self:_validate_and_merge(nested_config, field_schema.schema, field_path)
            
        else
            -- Simple field validation
            local validated_value = self:_validate_field(config_value, field_schema, field_path)
            result[key] = validated_value
        end
    end
    
    return result
end

-- Validate individual field
function Config:_validate_field(value, field_schema, field_path)
    -- Use default if value is nil
    if value == nil then
        return field_schema.default
    end
    
    -- Type validation
    if field_schema.type and type(value) ~= field_schema.type then
        table.insert(self.validation_errors, 
                    field_path .. ": Expected " .. field_schema.type .. ", got " .. type(value))
        return field_schema.default
    end
    
    -- Allowed values validation
    if field_schema.allowed_values then
        local found = false
        for _, allowed in ipairs(field_schema.allowed_values) do
            if value == allowed then
                found = true
                break
            end
        end
        
        if not found then
            table.insert(self.validation_errors,
                        field_path .. ": Value '" .. tostring(value) .. "' not in allowed values")
            return field_schema.default
        end
    end
    
    -- Numeric range validation
    if field_schema.type == "number" then
        if field_schema.min and value < field_schema.min then
            table.insert(self.validation_errors,
                        field_path .. ": Value " .. value .. " below minimum " .. field_schema.min)
            return field_schema.min
        end
        
        if field_schema.max and value > field_schema.max then
            table.insert(self.validation_errors,
                        field_path .. ": Value " .. value .. " above maximum " .. field_schema.max)
            return field_schema.max
        end
    end
    
    return value
end

-- Extract default values from schema
function Config:_extract_defaults(schema)
    local defaults = {}
    
    for key, field_schema in pairs(schema) do
        if field_schema.type == "table" and field_schema.schema then
            defaults[key] = self:_extract_defaults(field_schema.schema)
        else
            defaults[key] = field_schema.default
        end
    end
    
    return defaults
end

-- Get configuration value
function Config:get(path)
    return Utils.get_nested_value(self.config, path)
end

-- Set configuration value
function Config:set(path, value)
    return Utils.set_nested_value(self.config, path, value)
end

-- Get entire configuration
function Config:get_all()
    return Utils.deep_copy(self.config)
end

-- Update configuration with partial values
function Config:update(partial_config)
    local merged = Utils.deep_merge(self.config, partial_config)
    return self:load(merged)
end

-- Validate current configuration
function Config:validate()
    local temp_errors = self.validation_errors
    self.validation_errors = {}
    
    self.config = self:_validate_and_merge(self.config, self.schema)
    
    local is_valid = #self.validation_errors == 0
    if not is_valid then
        self.validation_errors = temp_errors
    end
    
    return is_valid
end

-- Get validation errors
function Config:get_validation_errors()
    return Utils.deep_copy(self.validation_errors)
end

-- Export configuration to file
function Config:export_to_file(filename)
    local success, error = pcall(function()
        local file = io.open(filename, "w")
        if not file then
            error("Cannot open file for writing: " .. filename)
        end
        
        file:write("-- Madara OBF Configuration\n")
        file:write("-- Generated on " .. os.date() .. "\n\n")
        file:write("return " .. self:_serialize_table(self.config, 0))
        file:close()
    end)
    
    return success, error
end

-- Serialize table to Lua code
function Config:_serialize_table(t, indent)
    indent = indent or 0
    local indent_str = string.rep("  ", indent)
    local next_indent_str = string.rep("  ", indent + 1)
    
    local parts = {"{\n"}
    
    for key, value in pairs(t) do
        local key_str = type(key) == "string" and key:match("^[%a_][%w_]*$") and key or ("[" .. string.format("%q", key) .. "]")
        
        table.insert(parts, next_indent_str .. key_str .. " = ")
        
        if type(value) == "table" then
            table.insert(parts, self:_serialize_table(value, indent + 1))
        elseif type(value) == "string" then
            table.insert(parts, string.format("%q", value))
        else
            table.insert(parts, tostring(value))
        end
        
        table.insert(parts, ",\n")
    end
    
    table.insert(parts, indent_str .. "}")
    
    return table.concat(parts)
end

return Config

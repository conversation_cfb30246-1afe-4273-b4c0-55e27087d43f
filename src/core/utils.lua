-- Project Madara OBF - Core Utility Functions
-- Enhanced utility functions with security-focused implementations

local Utils = {}

-- Deep copy a table (handles circular references)
function Utils.deep_copy(original, seen)
    seen = seen or {}
    
    if type(original) ~= "table" then
        return original
    end
    
    if seen[original] then
        return seen[original]
    end
    
    local copy = {}
    seen[original] = copy
    
    for key, value in pairs(original) do
        copy[Utils.deep_copy(key, seen)] = Utils.deep_copy(value, seen)
    end
    
    return setmetatable(copy, getmetatable(original))
end

-- Deep merge two tables
function Utils.deep_merge(target, source)
    target = target or {}
    
    if type(source) ~= "table" then
        return source
    end
    
    for key, value in pairs(source) do
        if type(value) == "table" and type(target[key]) == "table" then
            target[key] = Utils.deep_merge(target[key], value)
        else
            target[key] = value
        end
    end
    
    return target
end

-- Get nested value from table using dot notation
function Utils.get_nested_value(table, path)
    if type(table) ~= "table" or type(path) ~= "string" then
        return nil
    end
    
    local current = table
    for key in path:gmatch("[^%.]+") do
        if type(current) ~= "table" or current[key] == nil then
            return nil
        end
        current = current[key]
    end
    
    return current
end

-- Set nested value in table using dot notation
function Utils.set_nested_value(table, path, value)
    if type(table) ~= "table" or type(path) ~= "string" then
        return false
    end
    
    local keys = {}
    for key in path:gmatch("[^%.]+") do
        table.insert(keys, key)
    end
    
    local current = table
    for i = 1, #keys - 1 do
        local key = keys[i]
        if type(current[key]) ~= "table" then
            current[key] = {}
        end
        current = current[key]
    end
    
    current[keys[#keys]] = value
    return true
end

-- Shuffle array using Fisher-Yates algorithm
function Utils.shuffle(array)
    local result = Utils.deep_copy(array)
    
    for i = #result, 2, -1 do
        local j = math.random(i)
        result[i], result[j] = result[j], result[i]
    end
    
    return result
end

-- Generate random string with specified length and character set
function Utils.random_string(length, charset)
    length = length or 10
    charset = charset or "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    
    local result = {}
    for i = 1, length do
        local index = math.random(#charset)
        table.insert(result, charset:sub(index, index))
    end
    
    return table.concat(result)
end

-- Generate cryptographically secure random bytes
function Utils.secure_random_bytes(count)
    local bytes = {}
    
    -- Use multiple entropy sources
    local time_seed = os.time() * 1000000 + (os.clock() * 1000000) % 1000000
    local memory_seed = tostring({}):match("0x(%x+)") or "0"
    
    math.randomseed(time_seed + tonumber(memory_seed, 16))
    
    for i = 1, count do
        table.insert(bytes, math.random(0, 255))
    end
    
    return bytes
end

-- Convert string to character array
function Utils.string_to_chars(str)
    local chars = {}
    for i = 1, #str do
        table.insert(chars, str:sub(i, i))
    end
    return chars
end

-- Convert character array to string
function Utils.chars_to_string(chars)
    return table.concat(chars)
end

-- Split string by delimiter
function Utils.split_string(str, delimiter)
    delimiter = delimiter or "%s"
    local result = {}
    
    for match in str:gmatch("([^" .. delimiter .. "]+)") do
        table.insert(result, match)
    end
    
    return result
end

-- Trim whitespace from string
function Utils.trim_string(str)
    return str:match("^%s*(.-)%s*$")
end

-- Check if string starts with prefix
function Utils.starts_with(str, prefix)
    return str:sub(1, #prefix) == prefix
end

-- Check if string ends with suffix
function Utils.ends_with(str, suffix)
    return str:sub(-#suffix) == suffix
end

-- Escape special characters for pattern matching
function Utils.escape_pattern(str)
    return str:gsub("([%^%$%(%)%%%.%[%]%*%+%-%?])", "%%%1")
end

-- Generate unique identifier
function Utils.generate_uuid()
    local template = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx"
    
    return template:gsub("[xy]", function(c)
        local v = (c == "x") and math.random(0, 0xf) or math.random(8, 0xb)
        return string.format("%x", v)
    end)
end

-- Calculate file hash (simple implementation)
function Utils.calculate_hash(data)
    local hash = 0
    
    for i = 1, #data do
        hash = ((hash * 31) + string.byte(data, i)) % 2147483647
    end
    
    return hash
end

-- Encode data to base64
function Utils.base64_encode(data)
    local chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
    local result = {}
    
    for i = 1, #data, 3 do
        local a, b, c = string.byte(data, i, i + 2)
        b = b or 0
        c = c or 0
        
        local bitmap = (a << 16) + (b << 8) + c
        
        table.insert(result, chars:sub(((bitmap >> 18) & 63) + 1, ((bitmap >> 18) & 63) + 1))
        table.insert(result, chars:sub(((bitmap >> 12) & 63) + 1, ((bitmap >> 12) & 63) + 1))
        table.insert(result, (i + 1 <= #data) and chars:sub(((bitmap >> 6) & 63) + 1, ((bitmap >> 6) & 63) + 1) or "=")
        table.insert(result, (i + 2 <= #data) and chars:sub((bitmap & 63) + 1, (bitmap & 63) + 1) or "=")
    end
    
    return table.concat(result)
end

-- Decode base64 data
function Utils.base64_decode(data)
    local chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
    local lookup = {}
    
    for i = 1, #chars do
        lookup[chars:sub(i, i)] = i - 1
    end
    
    data = data:gsub("[^" .. chars .. "=]", "")
    local result = {}
    
    for i = 1, #data, 4 do
        local a, b, c, d = data:sub(i, i), data:sub(i + 1, i + 1), data:sub(i + 2, i + 2), data:sub(i + 3, i + 3)
        
        a = lookup[a] or 0
        b = lookup[b] or 0
        c = lookup[c] or 0
        d = lookup[d] or 0
        
        local bitmap = (a << 18) + (b << 12) + (c << 6) + d
        
        table.insert(result, string.char((bitmap >> 16) & 255))
        if data:sub(i + 2, i + 2) ~= "=" then
            table.insert(result, string.char((bitmap >> 8) & 255))
        end
        if data:sub(i + 3, i + 3) ~= "=" then
            table.insert(result, string.char(bitmap & 255))
        end
    end
    
    return table.concat(result)
end

-- Check if table is empty
function Utils.is_empty_table(t)
    return type(t) == "table" and next(t) == nil
end

-- Get table size (works with non-array tables)
function Utils.table_size(t)
    if type(t) ~= "table" then
        return 0
    end

    local count = 0
    for _ in pairs(t) do
        count = count + 1
    end

    return count
end

-- Alias for table_size
Utils.table_length = Utils.table_size

-- Convert table to array of key-value pairs
function Utils.table_to_pairs(t)
    local pairs = {}
    
    for key, value in pairs(t) do
        table.insert(pairs, {key = key, value = value})
    end
    
    return pairs
end

-- Measure execution time of a function
function Utils.measure_time(func, ...)
    local start_time = os.clock()
    local results = {func(...)}
    local end_time = os.clock()
    
    return end_time - start_time, unpack(results)
end

-- Create a readonly table
function Utils.readonly(t)
    return setmetatable({}, {
        __index = t,
        __newindex = function()
            error("Attempt to modify readonly table")
        end,
        __metatable = false
    })
end

return Utils

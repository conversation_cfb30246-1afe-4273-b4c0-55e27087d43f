-- Project Madara OBF - Core Pipeline System
-- Advanced obfuscation pipeline with enhanced modularity and control
--
-- This pipeline system provides superior flexibility compared to Prometheus
-- with fine-grained control over obfuscation steps and advanced dependency management.

local Pipeline = {}

local Logger = require("core.logger")
local Utils = require("core.utils")
local AST = require("core.ast")

-- Pipeline step priorities (lower numbers execute first)
local STEP_PRIORITIES = {
    PRE_ANALYSIS = 100,
    STRING_OBFUSCATION = 200,
    VARIABLE_OBFUSCATION = 300,
    CONTROL_FLOW = 400,
    ANTI_DEBUG = 500,
    BYTECODE = 600,
    POST_PROCESSING = 700
}

-- Create new pipeline instance
function Pipeline.new(config)
    local instance = {
        config = config or {},
        steps = {},
        step_count = 0,
        statistics = {
            total_time = 0,
            step_times = {},
            transformations = 0,
            code_size_change = 0
        },
        logger = Logger.new(config.log_level or "info"),
        ast_parser = AST.new(config.lua_version or "5.1")
    }
    
    setmetatable(instance, {__index = Pipeline})
    return instance
end

-- Add obfuscation step to pipeline
function Pipeline:add_step(step, priority)
    if not step or type(step.apply) ~= "function" then
        error("Invalid step: must have an 'apply' method")
    end
    
    priority = priority or step.priority or STEP_PRIORITIES.CONTROL_FLOW
    
    local step_info = {
        step = step,
        priority = priority,
        name = step.name or "Unnamed Step",
        description = step.description or "No description",
        dependencies = step.dependencies or {},
        id = self.step_count + 1
    }
    
    table.insert(self.steps, step_info)
    self.step_count = self.step_count + 1
    
    -- Sort steps by priority
    table.sort(self.steps, function(a, b) return a.priority < b.priority end)
    
    self.logger:debug("Added step: " .. step_info.name .. " (priority: " .. priority .. ")")
end

-- Remove step from pipeline
function Pipeline:remove_step(step_name)
    for i, step_info in ipairs(self.steps) do
        if step_info.name == step_name then
            table.remove(self.steps, i)
            self.logger:debug("Removed step: " .. step_name)
            return true
        end
    end
    return false
end

-- Get step by name
function Pipeline:get_step(step_name)
    for _, step_info in ipairs(self.steps) do
        if step_info.name == step_name then
            return step_info.step
        end
    end
    return nil
end

-- Validate pipeline dependencies
function Pipeline:_validate_dependencies()
    local step_names = {}
    for _, step_info in ipairs(self.steps) do
        step_names[step_info.name] = true
    end
    
    for _, step_info in ipairs(self.steps) do
        for _, dependency in ipairs(step_info.dependencies) do
            if not step_names[dependency] then
                error("Step '" .. step_info.name .. "' depends on missing step: " .. dependency)
            end
        end
    end
end

-- Process source code through pipeline
function Pipeline:process(source_code, filename, analysis_result)
    filename = filename or "anonymous"
    local start_time = os.clock()
    
    self.logger:info("Processing " .. filename .. " through pipeline with " .. #self.steps .. " steps")
    
    -- Validate dependencies
    self:_validate_dependencies()
    
    -- Parse source code to AST
    local ast = self.ast_parser:parse(source_code)
    local original_size = #source_code
    
    -- Create processing context
    local context = {
        filename = filename,
        original_code = source_code,
        ast = ast,
        analysis = analysis_result or {},
        config = self.config,
        pipeline = self,
        metadata = {},
        transformations = {}
    }
    
    -- Apply each step in order
    for i, step_info in ipairs(self.steps) do
        local step_start_time = os.clock()
        
        self.logger:info("Applying step " .. i .. "/" .. #self.steps .. ": " .. step_info.name)
        
        -- Check if step should be skipped
        if self:_should_skip_step(step_info, context) then
            self.logger:debug("Skipping step: " .. step_info.name)
            goto continue
        end
        
        -- Apply step
        local success, result = pcall(step_info.step.apply, step_info.step, context)
        
        if not success then
            self.logger:error("Step '" .. step_info.name .. "' failed: " .. tostring(result))
            error("Pipeline execution failed at step: " .. step_info.name)
        end
        
        -- Update context if step returned new AST
        if result and type(result) == "table" and result.kind then
            context.ast = result
        end
        
        local step_time = os.clock() - step_start_time
        self.statistics.step_times[step_info.name] = step_time
        
        self.logger:debug("Step '" .. step_info.name .. "' completed in " .. 
                         string.format("%.3f", step_time) .. " seconds")
        
        ::continue::
    end
    
    -- Generate final code from AST
    local final_code = self.ast_parser:unparse(context.ast)
    
    -- Update statistics
    local total_time = os.clock() - start_time
    self.statistics.total_time = total_time
    self.statistics.code_size_change = #final_code - original_size
    self.statistics.transformations = #context.transformations
    
    self.logger:info("Pipeline processing completed in " .. 
                    string.format("%.3f", total_time) .. " seconds")
    self.logger:info("Code size change: " .. self.statistics.code_size_change .. " bytes")
    
    return final_code
end

-- Check if step should be skipped based on conditions
function Pipeline:_should_skip_step(step_info, context)
    local step = step_info.step
    
    -- Check step-specific skip conditions
    if step.should_skip and step:should_skip(context) then
        return true
    end
    
    -- Check configuration-based conditions
    if step.config_key and not Utils.get_nested_value(context.config, step.config_key) then
        return true
    end
    
    return false
end

-- Get pipeline statistics
function Pipeline:get_statistics()
    return Utils.deep_copy(self.statistics)
end

-- Reset pipeline statistics
function Pipeline:reset_statistics()
    self.statistics = {
        total_time = 0,
        step_times = {},
        transformations = 0,
        code_size_change = 0
    }
end

-- Get pipeline configuration
function Pipeline:get_config()
    return Utils.deep_copy(self.config)
end

-- Update pipeline configuration
function Pipeline:update_config(new_config)
    self.config = Utils.deep_merge(self.config, new_config)
end

-- List all steps in pipeline
function Pipeline:list_steps()
    local step_list = {}
    for _, step_info in ipairs(self.steps) do
        table.insert(step_list, {
            name = step_info.name,
            description = step_info.description,
            priority = step_info.priority,
            dependencies = step_info.dependencies
        })
    end
    return step_list
end

-- Export step priorities for use by obfuscation modules
Pipeline.PRIORITIES = STEP_PRIORITIES

return Pipeline

-- Project Madara OBF - Enhanced AST Parser and Manipulator
-- Advanced Abstract Syntax Tree handling with superior analysis capabilities

local AST = {}

local Utils = require("core.utils")
local Logger = require("core.logger")

-- AST Node Types (enhanced from Prometheus)
local NODE_TYPES = {
    -- Statements
    BLOCK = "Block",
    LOCAL_DECLARATION = "LocalDeclaration",
    ASSIGNMENT = "Assignment",
    FUNCTION_DECLARATION = "FunctionDeclaration",
    IF_STATEMENT = "IfStatement",
    WHILE_LOOP = "WhileLoop",
    FOR_LOOP = "ForLoop",
    FOR_IN_LOOP = "ForInLoop",
    REPEAT_LOOP = "RepeatLoop",
    BREAK_STATEMENT = "BreakStatement",
    CONTINUE_STATEMENT = "ContinueStatement",
    RETURN_STATEMENT = "ReturnStatement",
    DO_STATEMENT = "DoStatement",
    
    -- Expressions
    VARIABLE = "Variable",
    NUMBER_LITERAL = "NumberLiteral",
    STRING_LITERAL = "StringLiteral",
    BOOLEAN_LITERAL = "BooleanLiteral",
    NIL_LITERAL = "NilLiteral",
    TABLE_LITERAL = "TableLiteral",
    FUNCTION_LITERAL = "FunctionLiteral",
    BINARY_OPERATION = "BinaryOperation",
    UNARY_OPERATION = "UnaryOperation",
    FUNCTION_CALL = "FunctionCall",
    INDEX_ACCESS = "IndexAccess",
    MEMBER_ACCESS = "MemberAccess",
    VARARG = "Vararg",
    
    -- Special
    TOP_LEVEL = "TopLevel",
    SCOPE = "Scope"
}

-- Binary operators
local BINARY_OPERATORS = {
    "+", "-", "*", "/", "%", "^",
    "==", "~=", "<", "<=", ">", ">=",
    "and", "or",
    "..", 
    "<<", ">>", "&", "|", "~"  -- Lua 5.3+ bitwise operators
}

-- Unary operators
local UNARY_OPERATORS = {
    "-", "not", "#", "~"  -- Lua 5.3+ bitwise not
}

-- Create new AST parser instance
function AST.new(lua_version)
    lua_version = lua_version or "5.1"
    
    local instance = {
        lua_version = lua_version,
        logger = Logger.new("debug"),
        node_id_counter = 0,
        scope_stack = {},
        current_scope = nil
    }
    
    setmetatable(instance, {__index = AST})
    return instance
end

-- Generate unique node ID
function AST:_generate_node_id()
    self.node_id_counter = self.node_id_counter + 1
    return "node_" .. self.node_id_counter
end

-- Create AST node
function AST:create_node(node_type, properties)
    properties = properties or {}
    
    local node = {
        type = node_type,
        id = self:_generate_node_id(),
        line = properties.line or 0,
        column = properties.column or 0,
        parent = nil,
        children = {},
        metadata = {}
    }
    
    -- Add type-specific properties
    for key, value in pairs(properties) do
        if key ~= "line" and key ~= "column" then
            node[key] = value
        end
    end
    
    return node
end

-- Add child node
function AST:add_child(parent, child)
    if not parent or not child then
        return false
    end
    
    child.parent = parent
    table.insert(parent.children, child)
    return true
end

-- Remove child node
function AST:remove_child(parent, child)
    if not parent or not child then
        return false
    end
    
    for i, c in ipairs(parent.children) do
        if c == child then
            table.remove(parent.children, i)
            child.parent = nil
            return true
        end
    end
    
    return false
end

-- Find nodes by type
function AST:find_nodes_by_type(root, node_type)
    local results = {}
    
    local function traverse(node)
        if node.type == node_type then
            table.insert(results, node)
        end
        
        for _, child in ipairs(node.children or {}) do
            traverse(child)
        end
    end
    
    traverse(root)
    return results
end

-- Find nodes by predicate function
function AST:find_nodes(root, predicate)
    local results = {}
    
    local function traverse(node)
        if predicate(node) then
            table.insert(results, node)
        end
        
        for _, child in ipairs(node.children or {}) do
            traverse(child)
        end
    end
    
    traverse(root)
    return results
end

-- Replace node in tree
function AST:replace_node(old_node, new_node)
    if not old_node.parent then
        return false
    end
    
    local parent = old_node.parent
    for i, child in ipairs(parent.children) do
        if child == old_node then
            parent.children[i] = new_node
            new_node.parent = parent
            old_node.parent = nil
            return true
        end
    end
    
    return false
end

-- Clone node (deep copy)
function AST:clone_node(node)
    local clone = Utils.deep_copy(node)
    
    -- Regenerate IDs for cloned nodes
    local function regenerate_ids(n)
        n.id = self:_generate_node_id()
        for _, child in ipairs(n.children or {}) do
            regenerate_ids(child)
        end
    end
    
    regenerate_ids(clone)
    return clone
end

-- Basic Lua parser (simplified - would need full implementation)
function AST:parse(source_code)
    self.logger:debug("Parsing Lua source code")

    -- For now, we'll store the source code directly and apply string-based transformations
    -- This is a pragmatic approach until a full AST parser is implemented

    local root = self:create_node(NODE_TYPES.TOP_LEVEL, {
        source = source_code,
        original_source = source_code,
        statements = {},
        string_literals = self:_extract_string_literals(source_code),
        variables = self:_extract_variables(source_code),
        functions = self:_extract_functions(source_code)
    })

    self.logger:debug("Parsing completed")
    return root
end

-- Convert AST back to Lua code
function AST:unparse(ast_root)
    self.logger:debug("Unparsing AST to Lua code")

    -- For now, return the transformed source code directly
    if ast_root.type == NODE_TYPES.TOP_LEVEL and ast_root.source then
        self.logger:debug("Unparsing completed")
        return ast_root.source
    end

    -- Fallback to original unparsing logic
    local function unparse_node(node, indent)
        indent = indent or 0
        local indent_str = string.rep("  ", indent)

        if node.type == NODE_TYPES.TOP_LEVEL then
            local parts = {}
            for _, child in ipairs(node.children) do
                table.insert(parts, unparse_node(child, indent))
            end
            return table.concat(parts, "\n")

        elseif node.type == NODE_TYPES.BLOCK then
            local parts = {}
            for _, stmt in ipairs(node.statements or {}) do
                table.insert(parts, indent_str .. unparse_node(stmt, indent))
            end
            return table.concat(parts, "\n")

        elseif node.type == NODE_TYPES.STRING_LITERAL then
            return string.format('"%s"', node.value or "")

        elseif node.type == NODE_TYPES.NUMBER_LITERAL then
            return tostring(node.value or 0)

        elseif node.type == NODE_TYPES.VARIABLE then
            return node.name or "unknown"

        else
            -- Default handling for other node types
            return "-- " .. (node.type or "unknown")
        end
    end

    local result = unparse_node(ast_root)
    self.logger:debug("Unparsing completed")
    return result
end

-- Analyze AST for obfuscation opportunities
function AST:analyze(ast_root)
    local analysis = {
        string_literals = {},
        number_literals = {},
        variables = {},
        functions = {},
        control_structures = {},
        complexity_score = 0
    }
    
    local function analyze_node(node)
        if node.type == NODE_TYPES.STRING_LITERAL then
            table.insert(analysis.string_literals, node)
            
        elseif node.type == NODE_TYPES.NUMBER_LITERAL then
            table.insert(analysis.number_literals, node)
            
        elseif node.type == NODE_TYPES.VARIABLE then
            table.insert(analysis.variables, node)
            
        elseif node.type == NODE_TYPES.FUNCTION_DECLARATION or 
               node.type == NODE_TYPES.FUNCTION_LITERAL then
            table.insert(analysis.functions, node)
            analysis.complexity_score = analysis.complexity_score + 2
            
        elseif node.type == NODE_TYPES.IF_STATEMENT or
               node.type == NODE_TYPES.WHILE_LOOP or
               node.type == NODE_TYPES.FOR_LOOP then
            table.insert(analysis.control_structures, node)
            analysis.complexity_score = analysis.complexity_score + 1
        end
        
        for _, child in ipairs(node.children or {}) do
            analyze_node(child)
        end
    end
    
    analyze_node(ast_root)
    
    self.logger:debug("AST analysis completed - found " .. 
                     #analysis.string_literals .. " strings, " ..
                     #analysis.variables .. " variables, " ..
                     #analysis.functions .. " functions")
    
    return analysis
end

-- Validate AST structure
function AST:validate(ast_root)
    local errors = {}
    
    local function validate_node(node, path)
        path = path or "root"
        
        -- Check required properties
        if not node.type then
            table.insert(errors, path .. ": Missing node type")
        end
        
        if not node.id then
            table.insert(errors, path .. ": Missing node ID")
        end
        
        -- Validate parent-child relationships
        for i, child in ipairs(node.children or {}) do
            if child.parent ~= node then
                table.insert(errors, path .. ".children[" .. i .. "]: Invalid parent reference")
            end
            
            validate_node(child, path .. ".children[" .. i .. "]")
        end
    end
    
    validate_node(ast_root)
    
    return {
        valid = #errors == 0,
        errors = errors
    }
end

-- Extract string literals from source code
function AST:_extract_string_literals(source_code)
    local strings = {}
    local patterns = {
        '"([^"\\]*(\\.[^"\\]*)*)"',  -- Double quoted strings
        "'([^'\\]*(\\.[^'\\]*)*)'",  -- Single quoted strings
        '%[%[(.-)%]%]'               -- Multi-line strings
    }

    for _, pattern in ipairs(patterns) do
        for match in source_code:gmatch(pattern) do
            table.insert(strings, {
                value = match,
                type = NODE_TYPES.STRING_LITERAL
            })
        end
    end

    return strings
end

-- Extract variable names from source code
function AST:_extract_variables(source_code)
    local variables = {}
    local seen = {}

    -- Simple pattern to find variable-like identifiers
    for var in source_code:gmatch("([%a_][%w_]*)") do
        if not seen[var] and not self:_is_lua_keyword(var) then
            seen[var] = true
            table.insert(variables, {
                name = var,
                type = NODE_TYPES.VARIABLE
            })
        end
    end

    return variables
end

-- Extract function names from source code
function AST:_extract_functions(source_code)
    local functions = {}

    -- Pattern for function declarations
    for func_name in source_code:gmatch("function%s+([%a_][%w_]*)") do
        table.insert(functions, {
            name = func_name,
            type = NODE_TYPES.FUNCTION_DECLARATION
        })
    end

    -- Pattern for local function declarations
    for func_name in source_code:gmatch("local%s+function%s+([%a_][%w_]*)") do
        table.insert(functions, {
            name = func_name,
            type = NODE_TYPES.FUNCTION_DECLARATION
        })
    end

    return functions
end

-- Check if identifier is a Lua keyword
function AST:_is_lua_keyword(identifier)
    local keywords = {
        "and", "break", "do", "else", "elseif", "end", "false", "for",
        "function", "if", "in", "local", "nil", "not", "or", "repeat",
        "return", "then", "true", "until", "while"
    }

    for _, keyword in ipairs(keywords) do
        if identifier == keyword then
            return true
        end
    end

    return false
end

-- Export node types and operators
AST.NODE_TYPES = Utils.readonly(NODE_TYPES)
AST.BINARY_OPERATORS = Utils.readonly(BINARY_OPERATORS)
AST.UNARY_OPERATORS = Utils.readonly(UNARY_OPERATORS)

return AST

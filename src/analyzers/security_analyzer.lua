-- Project Madara OBF - Security Analyzer
-- Security assessment and threat modeling

local SecurityAnalyzer = {}

local Utils = require("core.utils")
local Logger = require("core.logger")

-- Create new security analyzer instance
function SecurityAnalyzer.new()
    local instance = {
        logger = Logger.new("info")
    }
    
    setmetatable(instance, {__index = SecurityAnalyzer})
    return instance
end

-- Assess security requirements
function SecurityAnalyzer:assess(source_code, config)
    local assessment = {
        threat_level = self:_assess_threat_level(source_code),
        protection_requirements = self:_determine_protection_requirements(source_code, config),
        recommended_settings = self:_recommend_settings(source_code, config),
        security_score = 0
    }
    
    assessment.security_score = self:_calculate_security_score(assessment)
    
    return assessment
end

-- Assess threat level
function SecurityAnalyzer:_assess_threat_level(source_code)
    local threat_indicators = {
        crypto_operations = 0,
        network_operations = 0,
        file_operations = 0,
        system_calls = 0,
        sensitive_data = 0
    }
    
    -- Check for cryptographic operations
    local crypto_patterns = {"encrypt", "decrypt", "hash", "cipher", "key"}
    for _, pattern in ipairs(crypto_patterns) do
        for match in source_code:gmatch(pattern) do
            threat_indicators.crypto_operations = threat_indicators.crypto_operations + 1
        end
    end
    
    -- Check for network operations
    local network_patterns = {"socket", "http", "tcp", "udp", "connect"}
    for _, pattern in ipairs(network_patterns) do
        for match in source_code:gmatch(pattern) do
            threat_indicators.network_operations = threat_indicators.network_operations + 1
        end
    end
    
    -- Check for file operations
    local file_patterns = {"io%.open", "io%.read", "io%.write", "file"}
    for _, pattern in ipairs(file_patterns) do
        for match in source_code:gmatch(pattern) do
            threat_indicators.file_operations = threat_indicators.file_operations + 1
        end
    end
    
    -- Calculate threat level
    local total_indicators = threat_indicators.crypto_operations +
                           threat_indicators.network_operations +
                           threat_indicators.file_operations +
                           threat_indicators.system_calls +
                           threat_indicators.sensitive_data
    
    if total_indicators > 10 then
        return "high"
    elseif total_indicators > 5 then
        return "medium"
    else
        return "low"
    end
end

-- Determine protection requirements
function SecurityAnalyzer:_determine_protection_requirements(source_code, config)
    local requirements = {
        string_protection = "standard",
        control_flow_protection = "standard",
        anti_debug_protection = "standard",
        bytecode_protection = "none"
    }
    
    -- Analyze code characteristics
    local has_sensitive_strings = source_code:find("password") or 
                                 source_code:find("secret") or 
                                 source_code:find("key") or
                                 source_code:find("token")
    
    if has_sensitive_strings then
        requirements.string_protection = "high"
    end
    
    local has_complex_logic = source_code:find("if") and 
                             source_code:find("for") and 
                             source_code:find("while")
    
    if has_complex_logic then
        requirements.control_flow_protection = "high"
    end
    
    local has_debug_sensitive = source_code:find("debug") or 
                               source_code:find("trace") or
                               source_code:find("log")
    
    if has_debug_sensitive then
        requirements.anti_debug_protection = "high"
    end
    
    return requirements
end

-- Recommend optimal settings
function SecurityAnalyzer:_recommend_settings(source_code, config)
    local recommendations = {
        security_level = "standard",
        specific_settings = {}
    }
    
    -- Analyze code size
    local code_size = #source_code
    if code_size > 10000 then
        recommendations.specific_settings.encryption_layers = 3
        recommendations.specific_settings.bogus_branches_ratio = 0.3
    elseif code_size > 5000 then
        recommendations.specific_settings.encryption_layers = 2
        recommendations.specific_settings.bogus_branches_ratio = 0.2
    else
        recommendations.specific_settings.encryption_layers = 1
        recommendations.specific_settings.bogus_branches_ratio = 0.1
    end
    
    -- Analyze string density
    local string_count = 0
    for str in source_code:gmatch('"[^"]*"') do
        string_count = string_count + 1
    end
    
    local string_density = string_count / (code_size / 100)
    if string_density > 5 then
        recommendations.specific_settings.use_steganography = true
        recommendations.specific_settings.polymorphic_decryption = true
    end
    
    return recommendations
end

-- Calculate security score
function SecurityAnalyzer:_calculate_security_score(assessment)
    local score = 0
    
    -- Base score from threat level
    if assessment.threat_level == "high" then
        score = score + 30
    elseif assessment.threat_level == "medium" then
        score = score + 20
    else
        score = score + 10
    end
    
    -- Add points for protection requirements
    for _, level in pairs(assessment.protection_requirements) do
        if level == "high" then
            score = score + 20
        elseif level == "standard" then
            score = score + 10
        end
    end
    
    return math.min(score, 100)
end

return SecurityAnalyzer

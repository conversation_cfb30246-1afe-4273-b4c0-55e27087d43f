-- Discord <PERSON><PERSON> Example for Madara OBF Testing
-- This demonstrates obfuscating Discord bot code with sensitive data

-- Discord Bo<PERSON> Configuration (will be heavily obfuscated)
local DISCORD_CONFIG = {
    TOKEN = "MTM4MzUyMjY5MTU0MTEwNjY2OA.EXAMPLE.TOKEN_PLACEHOLDER_FOR_DEMO",
    CLIENT_ID = "1383522691541106668",
    GUILD_ID = "123456789012345678",
    API_VERSION = "10",
    INTENTS = {
        GUILDS = 1,
        GUILD_MESSAGES = 512,
        MESSAGE_CONTENT = 32768
    }
}

-- Bot Commands and Responses (sensitive data to protect)
local BOT_COMMANDS = {
    ["!admin"] = {
        response = "Admin panel access granted",
        permission = "administrator",
        secret_key = "admin_secret_2024"
    },
    ["!premium"] = {
        response = "Premium features unlocked",
        permission = "premium",
        secret_key = "premium_unlock_key"
    },
    ["!exploit"] = {
        response = "Exploit protection activated",
        permission = "owner",
        secret_key = "exploit_defense_system"
    }
}

-- User Database (sensitive information)
local USER_DATABASE = {
    ["123456789"] = {
        username = "admin_user",
        role = "administrator",
        premium = true,
        api_key = "sk-admin-1234567890abcdef",
        permissions = {"all"}
    },
    ["987654321"] = {
        username = "premium_user", 
        role = "premium",
        premium = true,
        api_key = "sk-premium-abcdef1234567890",
        permissions = {"premium", "basic"}
    }
}

-- Discord API Functions (will be obfuscated)
local DiscordAPI = {}

function DiscordAPI.authenticate(token)
    -- Simulate authentication
    if token == DISCORD_CONFIG.TOKEN then
        print("Discord bot authenticated successfully")
        return true
    else
        print("Authentication failed - invalid token")
        return false
    end
end

function DiscordAPI.sendMessage(channel_id, content)
    -- Simulate sending message
    print("Sending to channel " .. channel_id .. ": " .. content)
    return {
        id = "msg_" .. math.random(100000, 999999),
        timestamp = os.time(),
        content = content
    }
end

function DiscordAPI.getUserInfo(user_id)
    local user = USER_DATABASE[user_id]
    if user then
        return {
            id = user_id,
            username = user.username,
            role = user.role,
            premium = user.premium
        }
    else
        return nil
    end
end

-- Command Processing (sensitive logic)
local function processCommand(user_id, command, args)
    local user = USER_DATABASE[user_id]
    if not user then
        return "Access denied - user not found"
    end
    
    local cmd_info = BOT_COMMANDS[command]
    if not cmd_info then
        return "Unknown command"
    end
    
    -- Permission check
    local has_permission = false
    for _, perm in ipairs(user.permissions) do
        if perm == cmd_info.permission or perm == "all" then
            has_permission = true
            break
        end
    end
    
    if not has_permission then
        return "Insufficient permissions"
    end
    
    -- Execute command with secret key validation
    local secret_validation = user.api_key .. cmd_info.secret_key
    local validation_hash = 0
    for i = 1, #secret_validation do
        validation_hash = ((validation_hash * 31) + string.byte(secret_validation, i)) % 2147483647
    end
    
    if validation_hash > 0 then -- Always true, but obfuscated
        return cmd_info.response .. " (Hash: " .. validation_hash .. ")"
    else
        return "Security validation failed"
    end
end

-- Main Bot Logic
local function runDiscordBot()
    print("=== Discord Bot Starting ===")
    
    -- Authenticate with Discord
    if not DiscordAPI.authenticate(DISCORD_CONFIG.TOKEN) then
        print("Failed to start bot - authentication error")
        return false
    end
    
    print("Bot online and ready!")
    
    -- Simulate message handling
    local test_messages = {
        {user_id = "123456789", content = "!admin"},
        {user_id = "987654321", content = "!premium"},
        {user_id = "123456789", content = "!exploit"},
        {user_id = "555555555", content = "!admin"} -- Unauthorized user
    }
    
    for _, msg in ipairs(test_messages) do
        print("\n--- Processing Message ---")
        print("User: " .. msg.user_id)
        print("Content: " .. msg.content)
        
        local command = msg.content:match("^!%w+")
        if command then
            local response = processCommand(msg.user_id, command, {})
            print("Bot Response: " .. response)
            
            -- Send response back
            DiscordAPI.sendMessage("general", response)
        end
    end
    
    return true
end

-- Security Functions (will be heavily obfuscated)
local function validateEnvironment()
    -- Check for debugging tools
    if debug and debug.gethook and debug.gethook() then
        print("WARNING: Debugging detected!")
        return false
    end
    
    -- Check execution environment
    local env_check = type(print) == "function" and type(tostring) == "function"
    if not env_check then
        print("WARNING: Environment tampering detected!")
        return false
    end
    
    return true
end

local function obfuscatedMain()
    -- Environment validation
    if not validateEnvironment() then
        print("Security check failed - terminating")
        return
    end
    
    -- Run the bot
    local success = runDiscordBot()
    
    if success then
        print("\n=== Bot Session Complete ===")
    else
        print("\n=== Bot Failed to Start ===")
    end
end

-- Execute the bot
obfuscatedMain()

-- Return configuration for external use (this will be obfuscated too)
return {
    bot_id = DISCORD_CONFIG.CLIENT_ID,
    version = "1.0.0",
    status = "operational"
}

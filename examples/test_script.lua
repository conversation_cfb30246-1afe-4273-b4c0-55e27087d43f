-- Test Script for Project Madara OBF
-- This script demonstrates various Lua constructs that will be obfuscated

-- String literals to be obfuscated
local greeting = "Hello, <PERSON>!"
local secret_message = "This is a secret message that should be heavily obfuscated"
local api_key = "sk-1234567890abcdef"

-- Numeric literals
local magic_number = 42
local pi_approximation = 3.14159
local large_number = 1000000

-- Function definitions
local function fibonacci(n)
    if n <= 1 then
        return n
    else
        return fibonacci(n - 1) + fi<PERSON><PERSON><PERSON>(n - 2)
    end
end

local function encrypt_data(data, key)
    local result = ""
    for i = 1, #data do
        local char = string.sub(data, i, i)
        local encrypted_char = string.char((string.byte(char) + key) % 256)
        result = result .. encrypted_char
    end
    return result
end

-- Control flow structures
local function complex_logic(input)
    if input > 100 then
        print("Large input detected: " .. tostring(input))
        for i = 1, 10 do
            if i % 2 == 0 then
                print("Even number: " .. i)
            else
                print("Odd number: " .. i)
            end
        end
    elseif input > 50 then
        print("Medium input: " .. tostring(input))
        local counter = 0
        while counter < input / 10 do
            counter = counter + 1
            print("Counter: " .. counter)
        end
    else
        print("Small input: " .. tostring(input))
        repeat
            input = input + 1
            print("Incrementing: " .. input)
        until input > 10
    end
end

-- Table operations
local data_table = {
    name = "Test User",
    age = 25,
    email = "<EMAIL>",
    preferences = {
        theme = "dark",
        language = "en",
        notifications = true
    }
}

-- Function with closures
local function create_counter(initial)
    local count = initial or 0
    return function()
        count = count + 1
        return count
    end
end

-- Advanced string operations
local function process_strings()
    local strings = {
        "First string",
        "Second string with numbers 123",
        "Third string with special chars !@#$%",
        "Fourth string with unicode: αβγδε"
    }
    
    for i, str in ipairs(strings) do
        print("Processing string " .. i .. ": " .. str)
        local processed = string.upper(str)
        local encrypted = encrypt_data(processed, magic_number)
        print("Encrypted: " .. encrypted)
    end
end

-- Error handling
local function safe_divide(a, b)
    if b == 0 then
        error("Division by zero!")
    end
    return a / b
end

local function test_error_handling()
    local success, result = pcall(safe_divide, 10, 0)
    if success then
        print("Result: " .. result)
    else
        print("Error caught: " .. result)
    end
end

-- Metatable usage
local Vector = {}
Vector.__index = Vector

function Vector.new(x, y)
    return setmetatable({x = x or 0, y = y or 0}, Vector)
end

function Vector:magnitude()
    return math.sqrt(self.x * self.x + self.y * self.y)
end

function Vector.__add(a, b)
    return Vector.new(a.x + b.x, a.y + b.y)
end

-- Coroutine example
local function producer()
    for i = 1, 5 do
        print("Producing: " .. i)
        coroutine.yield(i)
    end
end

local function test_coroutines()
    local co = coroutine.create(producer)
    while coroutine.status(co) ~= "dead" do
        local success, value = coroutine.resume(co)
        if success and value then
            print("Consumed: " .. value)
        end
    end
end

-- Main execution
local function main()
    print(greeting)
    print("Secret: " .. secret_message)
    
    -- Test fibonacci
    print("Fibonacci(10): " .. fibonacci(10))
    
    -- Test complex logic
    complex_logic(75)
    
    -- Test string processing
    process_strings()
    
    -- Test counter closure
    local counter = create_counter(5)
    print("Counter: " .. counter())
    print("Counter: " .. counter())
    print("Counter: " .. counter())
    
    -- Test error handling
    test_error_handling()
    
    -- Test vectors
    local v1 = Vector.new(3, 4)
    local v2 = Vector.new(1, 2)
    local v3 = v1 + v2
    print("Vector magnitude: " .. v1:magnitude())
    print("Vector sum: (" .. v3.x .. ", " .. v3.y .. ")")
    
    -- Test coroutines
    test_coroutines()
    
    -- Final message
    print("Test script execution completed!")
    print("API Key used: " .. api_key)
end

-- Execute main function
main()

-- Global variables for testing
_G.test_global = "This is a global variable"
_G.another_global = {
    nested = {
        value = "Deeply nested value"
    }
}

-- Module-like structure
local TestModule = {}

function TestModule.public_function()
    return "This is a public function"
end

local function private_function()
    return "This is a private function"
end

function TestModule.get_private_result()
    return private_function()
end

-- Export module
return TestModule

-- Test simple obfuscation
print("Testing simple obfuscation...")

local success, err = pcall(function()
    local SimpleObfuscator = require("src.obfuscators.strings.simple_obfuscator")
    
    local obfuscator = SimpleObfuscator.new({})
    
    local test_code = [[
print("Hello, World!")
local message = "This is a test"
print(message)
]]
    
    print("Original code:")
    print(test_code)
    print("\nObfuscated code:")
    
    local obfuscated = obfuscator:process_strings(test_code)
    print(obfuscated)
    
    print("\nTesting obfuscated code execution:")
    local func, load_err = load(obfuscated)
    if func then
        func()
        print("✓ Obfuscated code executed successfully!")
    else
        print("✗ Failed to load obfuscated code: " .. tostring(load_err))
    end
end)

if success then
    print("✓ Simple obfuscation test passed!")
else
    print("✗ Simple obfuscation test failed:")
    print(err)
end

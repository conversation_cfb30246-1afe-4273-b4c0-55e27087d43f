#!/bin/bash

# Project Madara OBF Discord Bot Setup Script
# Automated setup for the most advanced Lua obfuscator bot!

echo "🔥 Project Madara OBF Discord Bot Setup"
echo "========================================"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8 or higher."
    exit 1
fi

echo "✅ Python 3 found: $(python3 --version)"

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is not installed. Please install pip."
    exit 1
fi

echo "✅ pip3 found"

# Install requirements
echo "📦 Installing Python dependencies..."
pip3 install -r requirements.txt

if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully"
else
    echo "❌ Failed to install dependencies"
    exit 1
fi

# Check if Lu<PERSON> is installed
if ! command -v lua &> /dev/null; then
    echo "⚠️  Lua is not installed. The bot needs <PERSON><PERSON> to run the obfuscator."
    echo "   Please install Lua 5.1 or higher."
    echo "   Ubuntu/Debian: sudo apt-get install lua5.1"
    echo "   macOS: brew install lua"
    echo "   Windows: Download from https://www.lua.org/download.html"
else
    echo "✅ Lua found: $(lua -v 2>&1 | head -n1)"
fi

# Check if bot token is configured
if grep -q "YOUR_BOT_TOKEN_HERE" madara_bot.py; then
    echo ""
    echo "⚠️  IMPORTANT: Bot token not configured!"
    echo "   1. Go to https://discord.com/developers/applications"
    echo "   2. Create a new application and bot"
    echo "   3. Copy the bot token"
    echo "   4. Edit madara_bot.py and replace 'YOUR_BOT_TOKEN_HERE' with your token"
    echo ""
else
    echo "✅ Bot token appears to be configured"
fi

echo ""
echo "🚀 Setup complete! To run the bot:"
echo "   python3 madara_bot.py"
echo ""
echo "🔗 Invite the bot to your server:"
echo "   https://discord.com/api/oauth2/authorize?client_id=YOUR_CLIENT_ID&permissions=274877908992&scope=bot"
echo "   (Replace YOUR_CLIENT_ID with your application ID)"
echo ""
echo "📚 Commands:"
echo "   !madara help      - Show help"
echo "   !madara obfuscate - Obfuscate Lua code"
echo "   !madara demo      - See demonstration"
echo ""
echo "🔥 Project Madara OBF - Ready to DESTROY Prometheus!"

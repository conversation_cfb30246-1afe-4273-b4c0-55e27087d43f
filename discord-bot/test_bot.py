#!/usr/bin/env python3
"""
Test script for Madara OBF Discord Bot
"""

import asyncio
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from madara_bot import MadaraObfuscator

async def test_obfuscator():
    """Test the obfuscator functionality"""
    print("🔥 Testing Madara OBF Discord Bot Integration...")
    
    obfuscator = MadaraObfuscator()
    
    # Test code
    test_code = '''
local secret = "Hello World!"
local function greet(name)
    return "Hello, " .. name .. "!"
end

print(greet("<PERSON>ara"))
print(secret)
'''
    
    print("📝 Original code:")
    print(test_code)
    print("\n🔄 Obfuscating...")
    
    # Test obfuscation
    result = await obfuscator.obfuscate(
        test_code, 
        preset="maximum", 
        security_level="maximum"
    )
    
    if result["success"]:
        print("✅ Obfuscation successful!")
        print(f"📊 Original size: {result['original_size']} bytes")
        print(f"📊 Obfuscated size: {result['obfuscated_size']} bytes")
        print(f"📊 Size ratio: {result['size_ratio']:.1f}%")
        print(f"⏱️ Processing time: {result['processing_time']:.3f}s")
        print("\n🔥 Obfuscated code:")
        print(result["obfuscated_code"][:500] + "..." if len(result["obfuscated_code"]) > 500 else result["obfuscated_code"])
    else:
        print("❌ Obfuscation failed!")
        print(f"Error: {result['error']}")
        if "stdout" in result:
            print(f"Output: {result['stdout']}")

if __name__ == "__main__":
    asyncio.run(test_obfuscator())
